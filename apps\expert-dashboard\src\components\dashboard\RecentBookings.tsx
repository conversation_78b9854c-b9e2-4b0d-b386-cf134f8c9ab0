'use client';

import { CalendarIcon, UserIcon, CurrencyDollarIcon } from '@heroicons/react/24/outline';

const recentBookings = [
  {
    id: 1,
    client: 'سارة أحمد',
    service: 'تصميم موقع إلكتروني',
    date: '2024-01-15',
    time: '14:00',
    amount: 150,
    status: 'confirmed',
  },
  {
    id: 2,
    client: 'محمد علي',
    service: 'تطوير تطبيق موبايل',
    date: '2024-01-16',
    time: '10:00',
    amount: 300,
    status: 'pending',
  },
  {
    id: 3,
    client: 'فاطمة خالد',
    service: 'استشارة تقنية',
    date: '2024-01-17',
    time: '16:30',
    amount: 75,
    status: 'completed',
  },
  {
    id: 4,
    client: 'أحمد حسن',
    service: 'تصميم هوية بصرية',
    date: '2024-01-18',
    time: '11:00',
    amount: 200,
    status: 'confirmed',
  },
];

const statusColors: Record<string, string> = {
  confirmed: 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100',
  pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100',
  completed: 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100',
  cancelled: 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100',
};

const statusLabels: Record<string, string> = {
  confirmed: 'مؤكدة',
  pending: 'في الانتظار',
  completed: 'مكتملة',
  cancelled: 'ملغية',
};

export function RecentBookings() {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          الحجوزات الأخيرة
        </h3>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          آخر الحجوزات والمواعيد القادمة
        </p>
      </div>
      
      <div className="divide-y divide-gray-200 dark:divide-gray-700">
        {recentBookings.map((booking) => (
          <div key={booking.id} className="px-6 py-4 hover:bg-gray-50 dark:hover:bg-gray-700">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <div className="flex items-center space-x-3 rtl:space-x-reverse">
                  <UserIcon className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {booking.client}
                    </p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {booking.service}
                    </p>
                  </div>
                </div>
                
                <div className="mt-2 flex items-center space-x-4 rtl:space-x-reverse text-sm text-gray-500 dark:text-gray-400">
                  <div className="flex items-center">
                    <CalendarIcon className="h-4 w-4 ml-1 rtl:ml-0 rtl:mr-1" />
                    {booking.date} في {booking.time}
                  </div>
                  <div className="flex items-center">
                    <CurrencyDollarIcon className="h-4 w-4 ml-1 rtl:ml-0 rtl:mr-1" />
                    ${booking.amount}
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${statusColors[booking.status]}`}>
                  {statusLabels[booking.status]}
                </span>
                <button className="text-primary-600 dark:text-primary-400 hover:text-primary-500 text-sm font-medium">
                  عرض التفاصيل
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
      
      <div className="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-center">
        <button className="text-sm font-medium text-primary-600 dark:text-primary-400 hover:text-primary-500">
          عرض جميع الحجوزات
        </button>
      </div>
    </div>
  );
}
