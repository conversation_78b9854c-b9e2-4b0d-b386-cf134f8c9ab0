export declare const useTranslation: (namespace?: string) => {
    t: import("i18next").TFunction<string, undefined>;
    i18n: import("i18next").i18n;
    language: string;
    isRTL: boolean;
    isArabic: boolean;
    isEnglish: boolean;
    direction: "ltr" | "rtl";
    textAlign: "left" | "right";
    changeLanguage: (lng?: string, callback?: import("node_modules/i18next").Callback) => Promise<import("i18next").TFunction>;
};
export declare const useRTL: () => {
    isRTL: boolean;
    direction: "ltr" | "rtl";
    textAlign: "left" | "right";
    flexDirection: string;
    marginStart: (value: string | number) => {
        [x: string]: string | number;
    };
    marginEnd: (value: string | number) => {
        [x: string]: string | number;
    };
    paddingStart: (value: string | number) => {
        [x: string]: string | number;
    };
    paddingEnd: (value: string | number) => {
        [x: string]: string | number;
    };
};
export declare const useLocalization: () => {
    language: string;
    locale: string;
    formatDate: (date: Date | string, options?: Intl.DateTimeFormatOptions) => string;
    formatNumber: (number: number, options?: Intl.NumberFormatOptions) => string;
    formatCurrency: (amount: number, currency?: string) => string;
    formatRelativeTime: (date: Date | string) => string;
};
//# sourceMappingURL=hooks.d.ts.map