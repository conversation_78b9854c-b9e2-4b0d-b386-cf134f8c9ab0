(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[91],{6435:function(e,t,n){"use strict";n.d(t,{F:function(){return s},f:function(){return c}});var r=n(2265);let o=["light","dark"],i="(prefers-color-scheme: dark)",l="undefined"==typeof window,a=(0,r.createContext)(void 0),u={setTheme:e=>{},themes:[]},s=()=>{var e;return null!==(e=(0,r.useContext)(a))&&void 0!==e?e:u},c=e=>(0,r.useContext)(a)?r.createElement(r.Fragment,null,e.children):r.createElement(f,e),d=["light","dark"],f=({forcedTheme:e,disableTransitionOnChange:t=!1,enableSystem:n=!0,enableColorScheme:l=!0,storageKey:u="theme",themes:s=d,defaultTheme:c=n?"system":"light",attribute:f="data-theme",value:g,children:y,nonce:b})=>{let[E,w]=(0,r.useState)(()=>p(u,c)),[R,P]=(0,r.useState)(()=>p(u)),S=g?Object.values(g):s,T=(0,r.useCallback)(e=>{let r=e;if(!r)return;"system"===e&&n&&(r=v());let i=g?g[r]:r,a=t?h():null,u=document.documentElement;if("class"===f?(u.classList.remove(...S),i&&u.classList.add(i)):i?u.setAttribute(f,i):u.removeAttribute(f),l){let e=o.includes(c)?c:null,t=o.includes(r)?r:e;u.style.colorScheme=t}null==a||a()},[]),O=(0,r.useCallback)(e=>{w(e);try{localStorage.setItem(u,e)}catch(e){}},[e]),M=(0,r.useCallback)(t=>{let r=v(t);P(r),"system"===E&&n&&!e&&T("system")},[E,e]);(0,r.useEffect)(()=>{let e=window.matchMedia(i);return e.addListener(M),M(e),()=>e.removeListener(M)},[M]),(0,r.useEffect)(()=>{let e=e=>{e.key===u&&O(e.newValue||c)};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[O]),(0,r.useEffect)(()=>{T(null!=e?e:E)},[e,E]);let x=(0,r.useMemo)(()=>({theme:E,setTheme:O,forcedTheme:e,resolvedTheme:"system"===E?R:E,themes:n?[...s,"system"]:s,systemTheme:n?R:void 0}),[E,O,e,R,n,s]);return r.createElement(a.Provider,{value:x},r.createElement(m,{forcedTheme:e,disableTransitionOnChange:t,enableSystem:n,enableColorScheme:l,storageKey:u,themes:s,defaultTheme:c,attribute:f,value:g,children:y,attrs:S,nonce:b}),y)},m=(0,r.memo)(({forcedTheme:e,storageKey:t,attribute:n,enableSystem:l,enableColorScheme:a,defaultTheme:u,value:s,attrs:c,nonce:d})=>{let f="system"===u,m="class"===n?`var d=document.documentElement,c=d.classList;c.remove(${c.map(e=>`'${e}'`).join(",")});`:`var d=document.documentElement,n='${n}',s='setAttribute';`,p=a?o.includes(u)&&u?`if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'${u}'`:"if(e==='light'||e==='dark')d.style.colorScheme=e":"",h=(e,t=!1,r=!0)=>{let i=s?s[e]:e,l=t?e+"|| ''":`'${i}'`,u="";return a&&r&&!t&&o.includes(e)&&(u+=`d.style.colorScheme = '${e}';`),"class"===n?u+=t||i?`c.add(${l})`:"null":i&&(u+=`d[s](n,${l})`),u},v=e?`!function(){${m}${h(e)}}()`:l?`!function(){try{${m}var e=localStorage.getItem('${t}');if('system'===e||(!e&&${f})){var t='${i}',m=window.matchMedia(t);if(m.media!==t||m.matches){${h("dark")}}else{${h("light")}}}else if(e){${s?`var x=${JSON.stringify(s)};`:""}${h(s?"x[e]":"e",!0)}}${f?"":"else{"+h(u,!1,!1)+"}"}${p}}catch(e){}}()`:`!function(){try{${m}var e=localStorage.getItem('${t}');if(e){${s?`var x=${JSON.stringify(s)};`:""}${h(s?"x[e]":"e",!0)}}else{${h(u,!1,!1)};}${p}}catch(t){}}();`;return r.createElement("script",{nonce:d,dangerouslySetInnerHTML:{__html:v}})},()=>!0),p=(e,t)=>{let n;if(!l){try{n=localStorage.getItem(e)||void 0}catch(e){}return n||t}},h=()=>{let e=document.createElement("style");return e.appendChild(document.createTextNode("*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(e),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(e)},1)}},v=e=>(e||(e=window.matchMedia(i)),e.matches?"dark":"light")},9524:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addLocale",{enumerable:!0,get:function(){return r}}),n(3997);let r=function(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4549:function(e,t,n){"use strict";function r(e,t,n,r){return!1}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getDomainLocale",{enumerable:!0,get:function(){return r}}),n(3997),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8326:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return E}});let r=n(7675),o=r._(n(2265)),i=n(9121),l=n(8664),a=n(8130),u=n(6681),s=n(9524),c=n(6304),d=n(6313),f=n(1581),m=n(4549),p=n(9872),h=n(9706),v=new Set;function g(e,t,n,r,o,i){if(!i&&!(0,l.isLocalURL)(t))return;if(!r.bypassPrefetchedCheck){let o=void 0!==r.locale?r.locale:"locale"in e?e.locale:void 0,i=t+"%"+n+"%"+o;if(v.has(i))return;v.add(i)}let a=i?e.prefetch(t,o):e.prefetch(t,n,r);Promise.resolve(a).catch(e=>{})}function y(e){return"string"==typeof e?e:(0,a.formatUrl)(e)}let b=o.default.forwardRef(function(e,t){let n,r;let{href:a,as:v,children:b,prefetch:E=null,passHref:w,replace:R,shallow:P,scroll:S,locale:T,onClick:O,onMouseEnter:M,onTouchStart:x,legacyBehavior:C=!1,...k}=e;n=b,C&&("string"==typeof n||"number"==typeof n)&&(n=o.default.createElement("a",null,n));let L=o.default.useContext(c.RouterContext),N=o.default.useContext(d.AppRouterContext),I=null!=L?L:N,A=!L,j=!1!==E,F=null===E?h.PrefetchKind.AUTO:h.PrefetchKind.FULL,{href:_,as:D}=o.default.useMemo(()=>{if(!L){let e=y(a);return{href:e,as:v?y(v):e}}let[e,t]=(0,i.resolveHref)(L,a,!0);return{href:e,as:v?(0,i.resolveHref)(L,v):t||e}},[L,a,v]),H=o.default.useRef(_),$=o.default.useRef(D);C&&(r=o.default.Children.only(n));let U=C?r&&"object"==typeof r&&r.ref:t,[W,z,V]=(0,f.useIntersection)({rootMargin:"200px"}),B=o.default.useCallback(e=>{($.current!==D||H.current!==_)&&(V(),$.current=D,H.current=_),W(e),U&&("function"==typeof U?U(e):"object"==typeof U&&(U.current=e))},[D,U,_,V,W]);o.default.useEffect(()=>{I&&z&&j&&g(I,_,D,{locale:T},{kind:F},A)},[D,_,z,T,j,null==L?void 0:L.locale,I,A,F]);let Z={ref:B,onClick(e){C||"function"!=typeof O||O(e),C&&r.props&&"function"==typeof r.props.onClick&&r.props.onClick(e),I&&!e.defaultPrevented&&function(e,t,n,r,i,a,u,s,c){let{nodeName:d}=e.currentTarget,f="A"===d.toUpperCase();if(f&&(function(e){let t=e.currentTarget,n=t.getAttribute("target");return n&&"_self"!==n||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||!c&&!(0,l.isLocalURL)(n)))return;e.preventDefault();let m=()=>{let e=null==u||u;"beforePopState"in t?t[i?"replace":"push"](n,r,{shallow:a,locale:s,scroll:e}):t[i?"replace":"push"](r||n,{scroll:e})};c?o.default.startTransition(m):m()}(e,I,_,D,R,P,S,T,A)},onMouseEnter(e){C||"function"!=typeof M||M(e),C&&r.props&&"function"==typeof r.props.onMouseEnter&&r.props.onMouseEnter(e),I&&(j||!A)&&g(I,_,D,{locale:T,priority:!0,bypassPrefetchedCheck:!0},{kind:F},A)},onTouchStart(e){C||"function"!=typeof x||x(e),C&&r.props&&"function"==typeof r.props.onTouchStart&&r.props.onTouchStart(e),I&&(j||!A)&&g(I,_,D,{locale:T,priority:!0,bypassPrefetchedCheck:!0},{kind:F},A)}};if((0,u.isAbsoluteUrl)(D))Z.href=D;else if(!C||w||"a"===r.type&&!("href"in r.props)){let e=void 0!==T?T:null==L?void 0:L.locale,t=(null==L?void 0:L.isLocaleDomain)&&(0,m.getDomainLocale)(D,e,null==L?void 0:L.locales,null==L?void 0:L.domainLocales);Z.href=t||(0,p.addBasePath)((0,s.addLocale)(D,e,null==L?void 0:L.defaultLocale))}return C?o.default.cloneElement(r,Z):o.default.createElement("a",{...k,...Z},n)}),E=b;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2389:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{requestIdleCallback:function(){return n},cancelIdleCallback:function(){return r}});let n="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let t=Date.now();return self.setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})},1)},r="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9121:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"resolveHref",{enumerable:!0,get:function(){return d}});let r=n(5991),o=n(8130),i=n(8137),l=n(6681),a=n(3997),u=n(8664),s=n(9289),c=n(948);function d(e,t,n){let d;let f="string"==typeof t?t:(0,o.formatWithValidation)(t),m=f.match(/^[a-zA-Z]{1,}:\/\//),p=m?f.slice(m[0].length):f,h=p.split("?",1);if((h[0]||"").match(/(\/\/|\\)/)){console.error("Invalid href '"+f+"' passed to next/router in page: '"+e.pathname+"'. Repeated forward-slashes (//) or backslashes \\ are not valid in the href.");let t=(0,l.normalizeRepeatedSlashes)(p);f=(m?m[0]:"")+t}if(!(0,u.isLocalURL)(f))return n?[f]:f;try{d=new URL(f.startsWith("#")?e.asPath:e.pathname,"http://n")}catch(e){d=new URL("/","http://n")}try{let e=new URL(f,d);e.pathname=(0,a.normalizePathTrailingSlash)(e.pathname);let t="";if((0,s.isDynamicRoute)(e.pathname)&&e.searchParams&&n){let n=(0,r.searchParamsToUrlQuery)(e.searchParams),{result:l,params:a}=(0,c.interpolateAs)(e.pathname,e.pathname,n);l&&(t=(0,o.formatWithValidation)({pathname:l,hash:e.hash,query:(0,i.omit)(n,a)}))}let l=e.origin===d.origin?e.href.slice(e.origin.length):e.href;return n?[l,t||l]:l}catch(e){return n?[f]:f}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1581:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useIntersection",{enumerable:!0,get:function(){return u}});let r=n(2265),o=n(2389),i="function"==typeof IntersectionObserver,l=new Map,a=[];function u(e){let{rootRef:t,rootMargin:n,disabled:u}=e,s=u||!i,[c,d]=(0,r.useState)(!1),f=(0,r.useRef)(null),m=(0,r.useCallback)(e=>{f.current=e},[]);(0,r.useEffect)(()=>{if(i){if(s||c)return;let e=f.current;if(e&&e.tagName){let r=function(e,t,n){let{id:r,observer:o,elements:i}=function(e){let t;let n={root:e.root||null,margin:e.rootMargin||""},r=a.find(e=>e.root===n.root&&e.margin===n.margin);if(r&&(t=l.get(r)))return t;let o=new Map,i=new IntersectionObserver(e=>{e.forEach(e=>{let t=o.get(e.target),n=e.isIntersecting||e.intersectionRatio>0;t&&n&&t(n)})},e);return t={id:n,observer:i,elements:o},a.push(n),l.set(n,t),t}(n);return i.set(e,t),o.observe(e),function(){if(i.delete(e),o.unobserve(e),0===i.size){o.disconnect(),l.delete(r);let e=a.findIndex(e=>e.root===r.root&&e.margin===r.margin);e>-1&&a.splice(e,1)}}}(e,e=>e&&d(e),{root:null==t?void 0:t.current,rootMargin:n});return r}}else if(!c){let e=(0,o.requestIdleCallback)(()=>d(!0));return()=>(0,o.cancelIdleCallback)(e)}},[s,n,t,c,f.current]);let p=(0,r.useCallback)(()=>{d(!1)},[]);return[m,c,p]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4910:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return o}});let n=/[|\\{}()[\]^$+*?.-]/,r=/[|\\{}()[\]^$+*?.-]/g;function o(e){return n.test(e)?e.replace(r,"\\$&"):e}},6304:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RouterContext",{enumerable:!0,get:function(){return i}});let r=n(7675),o=r._(n(2265)),i=o.default.createContext(null)},8130:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{formatUrl:function(){return l},urlObjectKeys:function(){return a},formatWithValidation:function(){return u}});let r=n(8169),o=r._(n(5991)),i=/https?|ftp|gopher|file/;function l(e){let{auth:t,hostname:n}=e,r=e.protocol||"",l=e.pathname||"",a=e.hash||"",u=e.query||"",s=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?s=t+e.host:n&&(s=t+(~n.indexOf(":")?"["+n+"]":n),e.port&&(s+=":"+e.port)),u&&"object"==typeof u&&(u=String(o.urlQueryToSearchParams(u)));let c=e.search||u&&"?"+u||"";return r&&!r.endsWith(":")&&(r+=":"),e.slashes||(!r||i.test(r))&&!1!==s?(s="//"+(s||""),l&&"/"!==l[0]&&(l="/"+l)):s||(s=""),a&&"#"!==a[0]&&(a="#"+a),c&&"?"!==c[0]&&(c="?"+c),""+r+s+(l=l.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+a}let a=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function u(e){return l(e)}},9289:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{getSortedRoutes:function(){return r.getSortedRoutes},isDynamicRoute:function(){return o.isDynamicRoute}});let r=n(9255),o=n(5321)},948:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"interpolateAs",{enumerable:!0,get:function(){return i}});let r=n(1670),o=n(4586);function i(e,t,n){let i="",l=(0,o.getRouteRegex)(e),a=l.groups,u=(t!==e?(0,r.getRouteMatcher)(l)(t):"")||n;i=e;let s=Object.keys(a);return s.every(e=>{let t=u[e]||"",{repeat:n,optional:r}=a[e],o="["+(n?"...":"")+e+"]";return r&&(o=(t?"":"/")+"["+o+"]"),n&&!Array.isArray(t)&&(t=[t]),(r||e in u)&&(i=i.replace(o,n?t.map(e=>encodeURIComponent(e)).join("/"):encodeURIComponent(t))||"/")})||(i=""),{params:s,result:i}}},5321:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isDynamicRoute",{enumerable:!0,get:function(){return i}});let r=n(4507),o=/\/\[[^/]+?\](?=\/|$)/;function i(e){return(0,r.isInterceptionRouteAppPath)(e)&&(e=(0,r.extractInterceptionRouteInformation)(e).interceptedRoute),o.test(e)}},8664:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return i}});let r=n(6681),o=n(6746);function i(e){if(!(0,r.isAbsoluteUrl)(e))return!0;try{let t=(0,r.getLocationOrigin)(),n=new URL(e,t);return n.origin===t&&(0,o.hasBasePath)(n.pathname)}catch(e){return!1}}},8137:function(e,t){"use strict";function n(e,t){let n={};return Object.keys(e).forEach(r=>{t.includes(r)||(n[r]=e[r])}),n}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"omit",{enumerable:!0,get:function(){return n}})},5991:function(e,t){"use strict";function n(e){let t={};return e.forEach((e,n)=>{void 0===t[n]?t[n]=e:Array.isArray(t[n])?t[n].push(e):t[n]=[t[n],e]}),t}function r(e){return"string"!=typeof e&&("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function o(e){let t=new URLSearchParams;return Object.entries(e).forEach(e=>{let[n,o]=e;Array.isArray(o)?o.forEach(e=>t.append(n,r(e))):t.set(n,r(o))}),t}function i(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return n.forEach(t=>{Array.from(t.keys()).forEach(t=>e.delete(t)),t.forEach((t,n)=>e.append(n,t))}),e}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{searchParamsToUrlQuery:function(){return n},urlQueryToSearchParams:function(){return o},assign:function(){return i}})},1670:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return o}});let r=n(6681);function o(e){let{re:t,groups:n}=e;return e=>{let o=t.exec(e);if(!o)return!1;let i=e=>{try{return decodeURIComponent(e)}catch(e){throw new r.DecodeError("failed to decode param")}},l={};return Object.keys(n).forEach(e=>{let t=n[e],r=o[t.pos];void 0!==r&&(l[e]=~r.indexOf("/")?r.split("/").map(e=>i(e)):t.repeat?[i(r)]:i(r))}),l}}},4586:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{getRouteRegex:function(){return u},getNamedRouteRegex:function(){return d},getNamedMiddlewareRegex:function(){return f}});let r=n(4507),o=n(4910),i=n(9006);function l(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let n=e.startsWith("...");return n&&(e=e.slice(3)),{key:e,repeat:n,optional:t}}function a(e){let t=(0,i.removeTrailingSlash)(e).slice(1).split("/"),n={},a=1;return{parameterizedRoute:t.map(e=>{let t=r.INTERCEPTION_ROUTE_MARKERS.find(t=>e.startsWith(t)),i=e.match(/\[((?:\[.*\])|.+)\]/);if(t&&i){let{key:e,optional:r,repeat:u}=l(i[1]);return n[e]={pos:a++,repeat:u,optional:r},"/"+(0,o.escapeStringRegexp)(t)+"([^/]+?)"}if(!i)return"/"+(0,o.escapeStringRegexp)(e);{let{key:e,repeat:t,optional:r}=l(i[1]);return n[e]={pos:a++,repeat:t,optional:r},t?r?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)"}}).join(""),groups:n}}function u(e){let{parameterizedRoute:t,groups:n}=a(e);return{re:RegExp("^"+t+"(?:/)?$"),groups:n}}function s(e){let{getSafeRouteKey:t,segment:n,routeKeys:r,keyPrefix:o}=e,{key:i,optional:a,repeat:u}=l(n),s=i.replace(/\W/g,"");o&&(s=""+o+s);let c=!1;return(0===s.length||s.length>30)&&(c=!0),isNaN(parseInt(s.slice(0,1)))||(c=!0),c&&(s=t()),o?r[s]=""+o+i:r[s]=""+i,u?a?"(?:/(?<"+s+">.+?))?":"/(?<"+s+">.+?)":"/(?<"+s+">[^/]+?)"}function c(e,t){let n;let l=(0,i.removeTrailingSlash)(e).slice(1).split("/"),a=(n=0,()=>{let e="",t=++n;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),u={};return{namedParameterizedRoute:l.map(e=>{let n=r.INTERCEPTION_ROUTE_MARKERS.some(t=>e.startsWith(t)),i=e.match(/\[((?:\[.*\])|.+)\]/);return n&&i?s({getSafeRouteKey:a,segment:i[1],routeKeys:u,keyPrefix:t?"nxtI":void 0}):i?s({getSafeRouteKey:a,segment:i[1],routeKeys:u,keyPrefix:t?"nxtP":void 0}):"/"+(0,o.escapeStringRegexp)(e)}).join(""),routeKeys:u}}function d(e,t){let n=c(e,t);return{...u(e),namedRegex:"^"+n.namedParameterizedRoute+"(?:/)?$",routeKeys:n.routeKeys}}function f(e,t){let{parameterizedRoute:n}=a(e),{catchAll:r=!0}=t;if("/"===n)return{namedRegex:"^/"+(r?".*":"")+"$"};let{namedParameterizedRoute:o}=c(e,!1);return{namedRegex:"^"+o+(r?"(?:(/.*)?)":"")+"$"}}},9255:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getSortedRoutes",{enumerable:!0,get:function(){return r}});class n{insert(e){this._insert(e.split("/").filter(Boolean),[],!1)}smoosh(){return this._smoosh()}_smoosh(e){void 0===e&&(e="/");let t=[...this.children.keys()].sort();null!==this.slugName&&t.splice(t.indexOf("[]"),1),null!==this.restSlugName&&t.splice(t.indexOf("[...]"),1),null!==this.optionalRestSlugName&&t.splice(t.indexOf("[[...]]"),1);let n=t.map(t=>this.children.get(t)._smoosh(""+e+t+"/")).reduce((e,t)=>[...e,...t],[]);if(null!==this.slugName&&n.push(...this.children.get("[]")._smoosh(e+"["+this.slugName+"]/")),!this.placeholder){let t="/"===e?"/":e.slice(0,-1);if(null!=this.optionalRestSlugName)throw Error('You cannot define a route with the same specificity as a optional catch-all route ("'+t+'" and "'+t+"[[..."+this.optionalRestSlugName+']]").');n.unshift(t)}return null!==this.restSlugName&&n.push(...this.children.get("[...]")._smoosh(e+"[..."+this.restSlugName+"]/")),null!==this.optionalRestSlugName&&n.push(...this.children.get("[[...]]")._smoosh(e+"[[..."+this.optionalRestSlugName+"]]/")),n}_insert(e,t,r){if(0===e.length){this.placeholder=!1;return}if(r)throw Error("Catch-all must be the last part of the URL.");let o=e[0];if(o.startsWith("[")&&o.endsWith("]")){let n=o.slice(1,-1),l=!1;if(n.startsWith("[")&&n.endsWith("]")&&(n=n.slice(1,-1),l=!0),n.startsWith("...")&&(n=n.substring(3),r=!0),n.startsWith("[")||n.endsWith("]"))throw Error("Segment names may not start or end with extra brackets ('"+n+"').");if(n.startsWith("."))throw Error("Segment names may not start with erroneous periods ('"+n+"').");function i(e,n){if(null!==e&&e!==n)throw Error("You cannot use different slug names for the same dynamic path ('"+e+"' !== '"+n+"').");t.forEach(e=>{if(e===n)throw Error('You cannot have the same slug name "'+n+'" repeat within a single dynamic path');if(e.replace(/\W/g,"")===o.replace(/\W/g,""))throw Error('You cannot have the slug names "'+e+'" and "'+n+'" differ only by non-word symbols within a single dynamic path')}),t.push(n)}if(r){if(l){if(null!=this.restSlugName)throw Error('You cannot use both an required and optional catch-all route at the same level ("[...'+this.restSlugName+']" and "'+e[0]+'" ).');i(this.optionalRestSlugName,n),this.optionalRestSlugName=n,o="[[...]]"}else{if(null!=this.optionalRestSlugName)throw Error('You cannot use both an optional and required catch-all route at the same level ("[[...'+this.optionalRestSlugName+']]" and "'+e[0]+'").');i(this.restSlugName,n),this.restSlugName=n,o="[...]"}}else{if(l)throw Error('Optional route parameters are not yet supported ("'+e[0]+'").');i(this.slugName,n),this.slugName=n,o="[]"}}this.children.has(o)||this.children.set(o,new n),this.children.get(o)._insert(e.slice(1),t,r)}constructor(){this.placeholder=!0,this.children=new Map,this.slugName=null,this.restSlugName=null,this.optionalRestSlugName=null}}function r(e){let t=new n;return e.forEach(e=>t.insert(e)),t.smoosh()}},6681:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{WEB_VITALS:function(){return n},execOnce:function(){return r},isAbsoluteUrl:function(){return i},getLocationOrigin:function(){return l},getURL:function(){return a},getDisplayName:function(){return u},isResSent:function(){return s},normalizeRepeatedSlashes:function(){return c},loadGetInitialProps:function(){return d},SP:function(){return f},ST:function(){return m},DecodeError:function(){return p},NormalizeError:function(){return h},PageNotFoundError:function(){return v},MissingStaticPage:function(){return g},MiddlewareNotFoundError:function(){return y},stringifyError:function(){return b}});let n=["CLS","FCP","FID","INP","LCP","TTFB"];function r(e){let t,n=!1;return function(){for(var r=arguments.length,o=Array(r),i=0;i<r;i++)o[i]=arguments[i];return n||(n=!0,t=e(...o)),t}}let o=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,i=e=>o.test(e);function l(){let{protocol:e,hostname:t,port:n}=window.location;return e+"//"+t+(n?":"+n:"")}function a(){let{href:e}=window.location,t=l();return e.substring(t.length)}function u(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function s(e){return e.finished||e.headersSent}function c(e){let t=e.split("?"),n=t[0];return n.replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let n=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let r=await e.getInitialProps(t);if(n&&s(n))return r;if(!r){let t='"'+u(e)+'.getInitialProps()" should resolve to an object. But found "'+r+'" instead.';throw Error(t)}return r}let f="undefined"!=typeof performance,m=f&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class p extends Error{}class h extends Error{}class v extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class g extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class y extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}},622:function(e,t,n){"use strict";var r=n(2265),o=Symbol.for("react.element"),i=Symbol.for("react.fragment"),l=Object.prototype.hasOwnProperty,a=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,u={key:!0,ref:!0,__self:!0,__source:!0};function s(e,t,n){var r,i={},s=null,c=null;for(r in void 0!==n&&(s=""+n),void 0!==t.key&&(s=""+t.key),void 0!==t.ref&&(c=t.ref),t)l.call(t,r)&&!u.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===i[r]&&(i[r]=t[r]);return{$$typeof:o,type:e,key:s,ref:c,props:i,_owner:a.current}}t.Fragment=i,t.jsx=s,t.jsxs=s},7437:function(e,t,n){"use strict";e.exports=n(622)},1396:function(e,t,n){e.exports=n(8326)},4033:function(e,t,n){e.exports=n(94)},4781:function(e,t,n){"use strict";let r,o;n.d(t,{V:function(){return ew}});var i,l,a,u,s,c,d,f=n(2265),m=n.t(f,2),p=n(2769),h=n(2950),v=n(1858);function g(e,t,n,r){let o=(0,v.E)(n);(0,f.useEffect)(()=>{function n(e){o.current(e)}return(e=null!=e?e:window).addEventListener(t,n,r),()=>e.removeEventListener(t,n,r)},[e,t,r])}var y=n(634),b=n(5195);function E(e){let t=(0,h.z)(e),n=(0,f.useRef)(!1);(0,f.useEffect)(()=>(n.current=!1,()=>{n.current=!0,(0,b.Y)(()=>{n.current&&t()})}),[t])}var w=n(9888),R=n(8957),P=n(6618),S=n(7976),T=((i=T||{})[i.Forwards=0]="Forwards",i[i.Backwards=1]="Backwards",i);function O(e,t){let n=(0,f.useRef)([]),r=(0,h.z)(e);(0,f.useEffect)(()=>{let e=[...n.current];for(let[o,i]of t.entries())if(n.current[o]!==i){let o=r(t,e);return n.current=t,o}},[r,...t])}var M=n(1931),x=((l=x||{})[l.None=1]="None",l[l.Focusable=2]="Focusable",l[l.Hidden=4]="Hidden",l);let C=(0,M.yV)(function(e,t){var n;let{features:r=1,...o}=e,i={ref:t,"aria-hidden":(2&r)==2||(null!=(n=o["aria-hidden"])?n:void 0),hidden:(4&r)==4||void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...(4&r)==4&&(2&r)!=2&&{display:"none"}}};return(0,M.sY)({ourProps:i,theirProps:o,slot:{},defaultTag:"div",name:"Hidden"})}),k=[];!function(e){function t(){"loading"!==document.readyState&&(e(),document.removeEventListener("DOMContentLoaded",t))}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("DOMContentLoaded",t),t())}(()=>{function e(e){e.target instanceof HTMLElement&&e.target!==document.body&&k[0]!==e.target&&(k.unshift(e.target),(k=k.filter(e=>null!=e&&e.isConnected)).splice(10))}window.addEventListener("click",e,{capture:!0}),window.addEventListener("mousedown",e,{capture:!0}),window.addEventListener("focus",e,{capture:!0}),document.body.addEventListener("click",e,{capture:!0}),document.body.addEventListener("mousedown",e,{capture:!0}),document.body.addEventListener("focus",e,{capture:!0})});var L=n(5410),N=n(597);function I(e){if(!e)return new Set;if("function"==typeof e)return new Set(e());let t=new Set;for(let n of e.current)n.current instanceof HTMLElement&&t.add(n.current);return t}var A=((a=A||{})[a.None=1]="None",a[a.InitialFocus=2]="InitialFocus",a[a.TabLock=4]="TabLock",a[a.FocusLock=8]="FocusLock",a[a.RestoreFocus=16]="RestoreFocus",a[a.All=30]="All",a);let j=Object.assign((0,M.yV)(function(e,t){let n,r=(0,f.useRef)(null),o=(0,P.T)(r,t),{initialFocus:i,containers:l,features:a=30,...u}=e;(0,R.H)()||(a=1);let s=(0,w.i)(r);!function({ownerDocument:e},t){let n=function(e=!0){let t=(0,f.useRef)(k.slice());return O(([e],[n])=>{!0===n&&!1===e&&(0,b.Y)(()=>{t.current.splice(0)}),!1===n&&!0===e&&(t.current=k.slice())},[e,k,t]),(0,h.z)(()=>{var e;return null!=(e=t.current.find(e=>null!=e&&e.isConnected))?e:null})}(t);O(()=>{t||(null==e?void 0:e.activeElement)===(null==e?void 0:e.body)&&(0,L.C5)(n())},[t]),E(()=>{t&&(0,L.C5)(n())})}({ownerDocument:s},!!(16&a));let c=function({ownerDocument:e,container:t,initialFocus:n},r){let o=(0,f.useRef)(null),i=(0,y.t)();return O(()=>{if(!r)return;let l=t.current;l&&(0,b.Y)(()=>{if(!i.current)return;let t=null==e?void 0:e.activeElement;if(null!=n&&n.current){if((null==n?void 0:n.current)===t){o.current=t;return}}else if(l.contains(t)){o.current=t;return}null!=n&&n.current?(0,L.C5)(n.current):(0,L.jA)(l,L.TO.First)===L.fE.Error&&console.warn("There are no focusable elements inside the <FocusTrap />"),o.current=null==e?void 0:e.activeElement})},[r]),o}({ownerDocument:s,container:r,initialFocus:i},!!(2&a));!function({ownerDocument:e,container:t,containers:n,previousActiveElement:r},o){let i=(0,y.t)();g(null==e?void 0:e.defaultView,"focus",e=>{if(!o||!i.current)return;let l=I(n);t.current instanceof HTMLElement&&l.add(t.current);let a=r.current;if(!a)return;let u=e.target;u&&u instanceof HTMLElement?F(l,u)?(r.current=u,(0,L.C5)(u)):(e.preventDefault(),e.stopPropagation(),(0,L.C5)(a)):(0,L.C5)(r.current)},!0)}({ownerDocument:s,container:r,containers:l,previousActiveElement:c},!!(8&a));let d=(n=(0,f.useRef)(0),(0,S.s)("keydown",e=>{"Tab"===e.key&&(n.current=e.shiftKey?1:0)},!0),n),m=(0,h.z)(e=>{let t=r.current;t&&(0,N.E)(d.current,{[T.Forwards]:()=>{(0,L.jA)(t,L.TO.First,{skipElements:[e.relatedTarget]})},[T.Backwards]:()=>{(0,L.jA)(t,L.TO.Last,{skipElements:[e.relatedTarget]})}})}),v=(0,p.G)(),A=(0,f.useRef)(!1);return f.createElement(f.Fragment,null,!!(4&a)&&f.createElement(C,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:m,features:x.Focusable}),(0,M.sY)({ourProps:{ref:o,onKeyDown(e){"Tab"==e.key&&(A.current=!0,v.requestAnimationFrame(()=>{A.current=!1}))},onBlur(e){let t=I(l);r.current instanceof HTMLElement&&t.add(r.current);let n=e.relatedTarget;n instanceof HTMLElement&&"true"!==n.dataset.headlessuiFocusGuard&&(F(t,n)||(A.current?(0,L.jA)(r.current,(0,N.E)(d.current,{[T.Forwards]:()=>L.TO.Next,[T.Backwards]:()=>L.TO.Previous})|L.TO.WrapAround,{relativeTo:e.target}):e.target instanceof HTMLElement&&(0,L.C5)(e.target)))}},theirProps:u,defaultTag:"div",name:"FocusTrap"}),!!(4&a)&&f.createElement(C,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:m,features:x.Focusable}))}),{features:A});function F(e,t){for(let n of e)if(n.contains(t))return!0;return!1}var _=n(4887),D=n(2600);let H=(0,f.createContext)(!1);function $(e){return f.createElement(H.Provider,{value:e.force},e.children)}var U=n(2057);let W=f.Fragment,z=f.Fragment,V=(0,f.createContext)(null),B=(0,f.createContext)(null),Z=Object.assign((0,M.yV)(function(e,t){let n=(0,f.useRef)(null),r=(0,P.T)((0,P.h)(e=>{n.current=e}),t),o=(0,w.i)(n),i=function(e){let t=(0,f.useContext)(H),n=(0,f.useContext)(V),r=(0,w.i)(e),[o,i]=(0,f.useState)(()=>{if(!t&&null!==n||U.O.isServer)return null;let e=null==r?void 0:r.getElementById("headlessui-portal-root");if(e)return e;if(null===r)return null;let o=r.createElement("div");return o.setAttribute("id","headlessui-portal-root"),r.body.appendChild(o)});return(0,f.useEffect)(()=>{null!==o&&(null!=r&&r.body.contains(o)||null==r||r.body.appendChild(o))},[o,r]),(0,f.useEffect)(()=>{t||null!==n&&i(n.current)},[n,i,t]),o}(n),[l]=(0,f.useState)(()=>{var e;return U.O.isServer?null:null!=(e=null==o?void 0:o.createElement("div"))?e:null}),a=(0,f.useContext)(B),u=(0,R.H)();return(0,D.e)(()=>{!i||!l||i.contains(l)||(l.setAttribute("data-headlessui-portal",""),i.appendChild(l))},[i,l]),(0,D.e)(()=>{if(l&&a)return a.register(l)},[a,l]),E(()=>{var e;i&&l&&(l instanceof Node&&i.contains(l)&&i.removeChild(l),i.childNodes.length<=0&&(null==(e=i.parentElement)||e.removeChild(i)))}),u&&i&&l?(0,_.createPortal)((0,M.sY)({ourProps:{ref:r},theirProps:e,defaultTag:W,name:"Portal"}),l):null}),{Group:(0,M.yV)(function(e,t){let{target:n,...r}=e,o={ref:(0,P.T)(t)};return f.createElement(V.Provider,{value:n},(0,M.sY)({ourProps:o,theirProps:r,defaultTag:z,name:"Popover.Group"}))})}),{useState:Y,useEffect:K,useLayoutEffect:q,useDebugValue:G}=m;"undefined"!=typeof window&&void 0!==window.document&&window.document.createElement;let Q=m.useSyncExternalStore;var J=n(5390),X=n(4644);let ee=(u={PUSH(e,t){var n;let r=null!=(n=this.get(e))?n:{doc:e,count:0,d:(0,J.k)(),meta:new Set};return r.count++,r.meta.add(t),this.set(e,r),this},POP(e,t){let n=this.get(e);return n&&(n.count--,n.meta.delete(t)),this},SCROLL_PREVENT({doc:e,d:t,meta:n}){let r;let o={doc:e,d:t,meta:function(e){let t={};for(let n of e)Object.assign(t,n(t));return t}(n)},i=[(0,X.gn)()?{before({doc:e,d:t,meta:n}){function r(e){return n.containers.flatMap(e=>e()).some(t=>t.contains(e))}t.microTask(()=>{var n;if("auto"!==window.getComputedStyle(e.documentElement).scrollBehavior){let n=(0,J.k)();n.style(e.documentElement,"scrollBehavior","auto"),t.add(()=>t.microTask(()=>n.dispose()))}let o=null!=(n=window.scrollY)?n:window.pageYOffset,i=null;t.addEventListener(e,"click",t=>{if(t.target instanceof HTMLElement)try{let n=t.target.closest("a");if(!n)return;let{hash:o}=new URL(n.href),l=e.querySelector(o);l&&!r(l)&&(i=l)}catch{}},!0),t.addEventListener(e,"touchstart",e=>{if(e.target instanceof HTMLElement){if(r(e.target)){let n=e.target;for(;n.parentElement&&r(n.parentElement);)n=n.parentElement;t.style(n,"overscrollBehavior","contain")}else t.style(e.target,"touchAction","none")}}),t.addEventListener(e,"touchmove",e=>{if(e.target instanceof HTMLElement){if(r(e.target)){let t=e.target;for(;t.parentElement&&""!==t.dataset.headlessuiPortal&&!(t.scrollHeight>t.clientHeight||t.scrollWidth>t.clientWidth);)t=t.parentElement;""===t.dataset.headlessuiPortal&&e.preventDefault()}else e.preventDefault()}},{passive:!1}),t.add(()=>{var e;o!==(null!=(e=window.scrollY)?e:window.pageYOffset)&&window.scrollTo(0,o),i&&i.isConnected&&(i.scrollIntoView({block:"nearest"}),i=null)})})}}:{},{before({doc:e}){var t;let n=e.documentElement;r=(null!=(t=e.defaultView)?t:window).innerWidth-n.clientWidth},after({doc:e,d:t}){let n=e.documentElement,o=n.clientWidth-n.offsetWidth,i=r-o;t.style(n,"paddingRight",`${i}px`)}},{before({doc:e,d:t}){t.style(e.documentElement,"overflow","hidden")}}];i.forEach(({before:e})=>null==e?void 0:e(o)),i.forEach(({after:e})=>null==e?void 0:e(o))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}},r=new Map,o=new Set,{getSnapshot:()=>r,subscribe:e=>(o.add(e),()=>o.delete(e)),dispatch(e,...t){let n=u[e].call(r,...t);n&&(r=n,o.forEach(e=>e()))}});ee.subscribe(()=>{let e=ee.getSnapshot(),t=new Map;for(let[n]of e)t.set(n,n.documentElement.style.overflow);for(let n of e.values()){let e="hidden"===t.get(n.doc),r=0!==n.count;(r&&!e||!r&&e)&&ee.dispatch(n.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",n),0===n.count&&ee.dispatch("TEARDOWN",n)}});var et=n(5606);let en=new Map,er=new Map;function eo(e,t=!0){(0,D.e)(()=>{var n;if(!t)return;let r="function"==typeof e?e():e.current;if(!r)return;let o=null!=(n=er.get(r))?n:0;return er.set(r,o+1),0!==o||(en.set(r,{"aria-hidden":r.getAttribute("aria-hidden"),inert:r.inert}),r.setAttribute("aria-hidden","true"),r.inert=!0),function(){var e;if(!r)return;let t=null!=(e=er.get(r))?e:1;if(1===t?er.delete(r):er.set(r,t-1),1!==t)return;let n=en.get(r);n&&(null===n["aria-hidden"]?r.removeAttribute("aria-hidden"):r.setAttribute("aria-hidden",n["aria-hidden"]),r.inert=n.inert,en.delete(r))}},[e,t])}var ei=n(583),el=n(5306);let ea=(0,f.createContext)(()=>{});ea.displayName="StackContext";var eu=((s=eu||{})[s.Add=0]="Add",s[s.Remove=1]="Remove",s);function es({children:e,onUpdate:t,type:n,element:r,enabled:o}){let i=(0,f.useContext)(ea),l=(0,h.z)((...e)=>{null==t||t(...e),i(...e)});return(0,D.e)(()=>{let e=void 0===o||!0===o;return e&&l(0,n,r),()=>{e&&l(1,n,r)}},[l,n,r,o]),f.createElement(ea.Provider,{value:l},e)}var ec=n(5863);let ed=(0,f.createContext)(null),ef=Object.assign((0,M.yV)(function(e,t){let n=(0,et.M)(),{id:r=`headlessui-description-${n}`,...o}=e,i=function e(){let t=(0,f.useContext)(ed);if(null===t){let t=Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(t,e),t}return t}(),l=(0,P.T)(t);(0,D.e)(()=>i.register(r),[r,i.register]);let a={ref:l,...i.props,id:r};return(0,M.sY)({ourProps:a,theirProps:o,slot:i.slot||{},defaultTag:"p",name:i.name||"Description"})}),{});var em=n(3850),ep=((c=ep||{})[c.Open=0]="Open",c[c.Closed=1]="Closed",c),eh=((d=eh||{})[d.SetTitleId=0]="SetTitleId",d);let ev={0:(e,t)=>e.titleId===t.id?e:{...e,titleId:t.id}},eg=(0,f.createContext)(null);function ey(e){let t=(0,f.useContext)(eg);if(null===t){let t=Error(`<${e} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,ey),t}return t}function eb(e,t){return(0,N.E)(t.type,ev,e,t)}eg.displayName="DialogContext";let eE=M.AN.RenderStrategy|M.AN.Static,ew=Object.assign((0,M.yV)(function(e,t){let n,r,o,i,l,a=(0,et.M)(),{id:u=`headlessui-dialog-${a}`,open:s,onClose:c,initialFocus:d,role:m="dialog",__demoMode:p=!1,...v}=e,[y,b]=(0,f.useState)(0),E=(0,f.useRef)(!1);m="dialog"===m||"alertdialog"===m?m:(E.current||(E.current=!0,console.warn(`Invalid role [${m}] passed to <Dialog />. Only \`dialog\` and and \`alertdialog\` are supported. Using \`dialog\` instead.`)),"dialog");let S=(0,el.oJ)();void 0===s&&null!==S&&(s=(S&el.ZM.Open)===el.ZM.Open);let T=(0,f.useRef)(null),O=(0,P.T)(T,t),k=(0,w.i)(T),L=e.hasOwnProperty("open")||null!==S,I=e.hasOwnProperty("onClose");if(!L&&!I)throw Error("You have to provide an `open` and an `onClose` prop to the `Dialog` component.");if(!L)throw Error("You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.");if(!I)throw Error("You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.");if("boolean"!=typeof s)throw Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${s}`);if("function"!=typeof c)throw Error(`You provided an \`onClose\` prop to the \`Dialog\`, but the value is not a function. Received: ${c}`);let A=s?0:1,[F,_]=(0,f.useReducer)(eb,{titleId:null,descriptionId:null,panelRef:(0,f.createRef)()}),H=(0,h.z)(()=>c(!1)),U=(0,h.z)(e=>_({type:0,id:e})),W=!!(0,R.H)()&&!p&&0===A,z=y>1,V=null!==(0,f.useContext)(eg),[Y,K]=(n=(0,f.useContext)(B),r=(0,f.useRef)([]),o=(0,h.z)(e=>(r.current.push(e),n&&n.register(e),()=>i(e))),i=(0,h.z)(e=>{let t=r.current.indexOf(e);-1!==t&&r.current.splice(t,1),n&&n.unregister(e)}),l=(0,f.useMemo)(()=>({register:o,unregister:i,portals:r}),[o,i,r]),[r,(0,f.useMemo)(()=>function({children:e}){return f.createElement(B.Provider,{value:l},e)},[l])]),{resolveContainers:q,mainTreeNodeRef:G,MainTreeNode:J}=function({defaultContainers:e=[],portals:t,mainTreeNodeRef:n}={}){var r;let o=(0,f.useRef)(null!=(r=null==n?void 0:n.current)?r:null),i=(0,w.i)(o),l=(0,h.z)(()=>{var n,r,l;let a=[];for(let t of e)null!==t&&(t instanceof HTMLElement?a.push(t):"current"in t&&t.current instanceof HTMLElement&&a.push(t.current));if(null!=t&&t.current)for(let e of t.current)a.push(e);for(let e of null!=(n=null==i?void 0:i.querySelectorAll("html > *, body > *"))?n:[])e!==document.body&&e!==document.head&&e instanceof HTMLElement&&"headlessui-portal-root"!==e.id&&(e.contains(o.current)||e.contains(null==(l=null==(r=o.current)?void 0:r.getRootNode())?void 0:l.host)||a.some(t=>e.contains(t))||a.push(e));return a});return{resolveContainers:l,contains:(0,h.z)(e=>l().some(t=>t.contains(e))),mainTreeNodeRef:o,MainTreeNode:(0,f.useMemo)(()=>function(){return null!=n?null:f.createElement(C,{features:x.Hidden,ref:o})},[o,n])}}({portals:Y,defaultContainers:[{get current(){var X;return null!=(X=F.panelRef.current)?X:T.current}}]}),en=null!==S&&(S&el.ZM.Closing)===el.ZM.Closing,er=!V&&!en&&W;eo((0,f.useCallback)(()=>{var e,t;return null!=(t=Array.from(null!=(e=null==k?void 0:k.querySelectorAll("body > *"))?e:[]).find(e=>"headlessui-portal-root"!==e.id&&e.contains(G.current)&&e instanceof HTMLElement))?t:null},[G]),er);let ea=!!z||W;eo((0,f.useCallback)(()=>{var e,t;return null!=(t=Array.from(null!=(e=null==k?void 0:k.querySelectorAll("[data-headlessui-portal]"))?e:[]).find(e=>e.contains(G.current)&&e instanceof HTMLElement))?t:null},[G]),ea);let ec=!(!W||z);(0,ei.O)(q,e=>{e.preventDefault(),H()},ec);let ef=!(z||0!==A);g(null==k?void 0:k.defaultView,"keydown",e=>{ef&&(e.defaultPrevented||e.key===em.R.Escape&&(e.preventDefault(),e.stopPropagation(),H()))}),function(e,t,n=()=>[document.body]){var r;let o,i;r=e=>{var t;return{containers:[...null!=(t=e.containers)?t:[],n]}},o=Q(ee.subscribe,ee.getSnapshot,ee.getSnapshot),(i=e?o.get(e):void 0)&&i.count,(0,D.e)(()=>{if(!(!e||!t))return ee.dispatch("PUSH",e,r),()=>ee.dispatch("POP",e,r)},[t,e])}(k,!(en||0!==A||V),q),(0,f.useEffect)(()=>{if(0!==A||!T.current)return;let e=new ResizeObserver(e=>{for(let t of e){let e=t.target.getBoundingClientRect();0===e.x&&0===e.y&&0===e.width&&0===e.height&&H()}});return e.observe(T.current),()=>e.disconnect()},[A,T,H]);let[ep,eh]=function(){let[e,t]=(0,f.useState)([]);return[e.length>0?e.join(" "):void 0,(0,f.useMemo)(()=>function(e){let n=(0,h.z)(e=>(t(t=>[...t,e]),()=>t(t=>{let n=t.slice(),r=n.indexOf(e);return -1!==r&&n.splice(r,1),n}))),r=(0,f.useMemo)(()=>({register:n,slot:e.slot,name:e.name,props:e.props}),[n,e.slot,e.name,e.props]);return f.createElement(ed.Provider,{value:r},e.children)},[t])]}(),ev=(0,f.useMemo)(()=>[{dialogState:A,close:H,setTitleId:U},F],[A,F,H,U]),ey=(0,f.useMemo)(()=>({open:0===A}),[A]),ew={ref:O,id:u,role:m,"aria-modal":0===A||void 0,"aria-labelledby":F.titleId,"aria-describedby":ep};return f.createElement(es,{type:"Dialog",enabled:0===A,element:T,onUpdate:(0,h.z)((e,t)=>{"Dialog"===t&&(0,N.E)(e,{[eu.Add]:()=>b(e=>e+1),[eu.Remove]:()=>b(e=>e-1)})})},f.createElement($,{force:!0},f.createElement(Z,null,f.createElement(eg.Provider,{value:ev},f.createElement(Z.Group,{target:T},f.createElement($,{force:!1},f.createElement(eh,{slot:ey,name:"Dialog.Description"},f.createElement(j,{initialFocus:d,containers:q,features:W?(0,N.E)(z?"parent":"leaf",{parent:j.features.RestoreFocus,leaf:j.features.All&~j.features.FocusLock}):j.features.None},f.createElement(K,null,(0,M.sY)({ourProps:ew,theirProps:v,slot:ey,defaultTag:"div",features:eE,visible:0===A,name:"Dialog"}))))))))),f.createElement(J,null))}),{Backdrop:(0,M.yV)(function(e,t){let n=(0,et.M)(),{id:r=`headlessui-dialog-backdrop-${n}`,...o}=e,[{dialogState:i},l]=ey("Dialog.Backdrop"),a=(0,P.T)(t);(0,f.useEffect)(()=>{if(null===l.panelRef.current)throw Error("A <Dialog.Backdrop /> component is being used, but a <Dialog.Panel /> component is missing.")},[l.panelRef]);let u=(0,f.useMemo)(()=>({open:0===i}),[i]);return f.createElement($,{force:!0},f.createElement(Z,null,(0,M.sY)({ourProps:{ref:a,id:r,"aria-hidden":!0},theirProps:o,slot:u,defaultTag:"div",name:"Dialog.Backdrop"})))}),Panel:(0,M.yV)(function(e,t){let n=(0,et.M)(),{id:r=`headlessui-dialog-panel-${n}`,...o}=e,[{dialogState:i},l]=ey("Dialog.Panel"),a=(0,P.T)(t,l.panelRef),u=(0,f.useMemo)(()=>({open:0===i}),[i]),s=(0,h.z)(e=>{e.stopPropagation()});return(0,M.sY)({ourProps:{ref:a,id:r,onClick:s},theirProps:o,slot:u,defaultTag:"div",name:"Dialog.Panel"})}),Overlay:(0,M.yV)(function(e,t){let n=(0,et.M)(),{id:r=`headlessui-dialog-overlay-${n}`,...o}=e,[{dialogState:i,close:l}]=ey("Dialog.Overlay"),a=(0,P.T)(t),u=(0,h.z)(e=>{if(e.target===e.currentTarget){if((0,ec.P)(e.currentTarget))return e.preventDefault();e.preventDefault(),e.stopPropagation(),l()}}),s=(0,f.useMemo)(()=>({open:0===i}),[i]);return(0,M.sY)({ourProps:{ref:a,id:r,"aria-hidden":!0,onClick:u},theirProps:o,slot:s,defaultTag:"div",name:"Dialog.Overlay"})}),Title:(0,M.yV)(function(e,t){let n=(0,et.M)(),{id:r=`headlessui-dialog-title-${n}`,...o}=e,[{dialogState:i,setTitleId:l}]=ey("Dialog.Title"),a=(0,P.T)(t);(0,f.useEffect)(()=>(l(r),()=>l(null)),[r,l]);let u=(0,f.useMemo)(()=>({open:0===i}),[i]);return(0,M.sY)({ourProps:{ref:a,id:r},theirProps:o,slot:u,defaultTag:"h2",name:"Dialog.Title"})}),Description:ef})},3850:function(e,t,n){"use strict";n.d(t,{R:function(){return o}});var r,o=((r=o||{}).Space=" ",r.Enter="Enter",r.Escape="Escape",r.Backspace="Backspace",r.Delete="Delete",r.ArrowLeft="ArrowLeft",r.ArrowUp="ArrowUp",r.ArrowRight="ArrowRight",r.ArrowDown="ArrowDown",r.Home="Home",r.End="End",r.PageUp="PageUp",r.PageDown="PageDown",r.Tab="Tab",r)},2926:function(e,t,n){"use strict";n.d(t,{v:function(){return H}});var r,o,i,l,a=n(2265),u=n(2769),s=n(2950),c=n(5606),d=n(2600),f=n(583),m=n(9888);function p(e){var t;if(e.type)return e.type;let n=null!=(t=e.as)?t:"button";if("string"==typeof n&&"button"===n.toLowerCase())return"button"}var h=n(6618);let v=/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g;function g(e){var t,n;let r=null!=(t=e.innerText)?t:"",o=e.cloneNode(!0);if(!(o instanceof HTMLElement))return r;let i=!1;for(let e of o.querySelectorAll('[hidden],[aria-hidden],[role="img"]'))e.remove(),i=!0;let l=i?null!=(n=o.innerText)?n:"":r;return v.test(l)&&(l=l.replace(v,"")),l}function y(e){return[e.screenX,e.screenY]}var b=n(4851),E=n(5306),w=n(5863),R=((r=R||{})[r.First=0]="First",r[r.Previous=1]="Previous",r[r.Next=2]="Next",r[r.Last=3]="Last",r[r.Specific=4]="Specific",r[r.Nothing=5]="Nothing",r),P=n(5390),S=n(5410),T=n(597),O=n(1931),M=n(3850),x=((o=x||{})[o.Open=0]="Open",o[o.Closed=1]="Closed",o),C=((i=C||{})[i.Pointer=0]="Pointer",i[i.Other=1]="Other",i),k=((l=k||{})[l.OpenMenu=0]="OpenMenu",l[l.CloseMenu=1]="CloseMenu",l[l.GoToItem=2]="GoToItem",l[l.Search=3]="Search",l[l.ClearSearch=4]="ClearSearch",l[l.RegisterItem=5]="RegisterItem",l[l.UnregisterItem=6]="UnregisterItem",l);function L(e,t=e=>e){let n=null!==e.activeItemIndex?e.items[e.activeItemIndex]:null,r=(0,S.z2)(t(e.items.slice()),e=>e.dataRef.current.domRef.current),o=n?r.indexOf(n):null;return -1===o&&(o=null),{items:r,activeItemIndex:o}}let N={1:e=>1===e.menuState?e:{...e,activeItemIndex:null,menuState:1},0:e=>0===e.menuState?e:{...e,__demoMode:!1,menuState:0},2:(e,t)=>{var n;let r=L(e),o=function(e,t){let n=t.resolveItems();if(n.length<=0)return null;let r=t.resolveActiveIndex(),o=null!=r?r:-1;switch(e.focus){case 0:for(let e=0;e<n.length;++e)if(!t.resolveDisabled(n[e],e,n))return e;return r;case 1:for(let e=o-1;e>=0;--e)if(!t.resolveDisabled(n[e],e,n))return e;return r;case 2:for(let e=o+1;e<n.length;++e)if(!t.resolveDisabled(n[e],e,n))return e;return r;case 3:for(let e=n.length-1;e>=0;--e)if(!t.resolveDisabled(n[e],e,n))return e;return r;case 4:for(let r=0;r<n.length;++r)if(t.resolveId(n[r],r,n)===e.id)return r;return r;case 5:return null;default:!function(e){throw Error("Unexpected object: "+e)}(e)}}(t,{resolveItems:()=>r.items,resolveActiveIndex:()=>r.activeItemIndex,resolveId:e=>e.id,resolveDisabled:e=>e.dataRef.current.disabled});return{...e,...r,searchQuery:"",activeItemIndex:o,activationTrigger:null!=(n=t.trigger)?n:1}},3:(e,t)=>{let n=""!==e.searchQuery?0:1,r=e.searchQuery+t.value.toLowerCase(),o=(null!==e.activeItemIndex?e.items.slice(e.activeItemIndex+n).concat(e.items.slice(0,e.activeItemIndex+n)):e.items).find(e=>{var t;return(null==(t=e.dataRef.current.textValue)?void 0:t.startsWith(r))&&!e.dataRef.current.disabled}),i=o?e.items.indexOf(o):-1;return -1===i||i===e.activeItemIndex?{...e,searchQuery:r}:{...e,searchQuery:r,activeItemIndex:i,activationTrigger:1}},4:e=>""===e.searchQuery?e:{...e,searchQuery:"",searchActiveItemIndex:null},5:(e,t)=>{let n=L(e,e=>[...e,{id:t.id,dataRef:t.dataRef}]);return{...e,...n}},6:(e,t)=>{let n=L(e,e=>{let n=e.findIndex(e=>e.id===t.id);return -1!==n&&e.splice(n,1),e});return{...e,...n,activationTrigger:1}}},I=(0,a.createContext)(null);function A(e){let t=(0,a.useContext)(I);if(null===t){let t=Error(`<${e} /> is missing a parent <Menu /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,A),t}return t}function j(e,t){return(0,T.E)(t.type,N,e,t)}I.displayName="MenuContext";let F=a.Fragment,_=O.AN.RenderStrategy|O.AN.Static,D=a.Fragment,H=Object.assign((0,O.yV)(function(e,t){let{__demoMode:n=!1,...r}=e,o=(0,a.useReducer)(j,{__demoMode:n,menuState:n?0:1,buttonRef:(0,a.createRef)(),itemsRef:(0,a.createRef)(),items:[],searchQuery:"",activeItemIndex:null,activationTrigger:1}),[{menuState:i,itemsRef:l,buttonRef:u},c]=o,d=(0,h.T)(t);(0,f.O)([u,l],(e,t)=>{var n;c({type:1}),(0,S.sP)(t,S.tJ.Loose)||(e.preventDefault(),null==(n=u.current)||n.focus())},0===i);let m=(0,s.z)(()=>{c({type:1})}),p=(0,a.useMemo)(()=>({open:0===i,close:m}),[i,m]);return a.createElement(I.Provider,{value:o},a.createElement(E.up,{value:(0,T.E)(i,{0:E.ZM.Open,1:E.ZM.Closed})},(0,O.sY)({ourProps:{ref:d},theirProps:r,slot:p,defaultTag:F,name:"Menu"})))}),{Button:(0,O.yV)(function(e,t){var n;let r=(0,c.M)(),{id:o=`headlessui-menu-button-${r}`,...i}=e,[l,f]=A("Menu.Button"),m=(0,h.T)(l.buttonRef,t),v=(0,u.G)(),g=(0,s.z)(e=>{switch(e.key){case M.R.Space:case M.R.Enter:case M.R.ArrowDown:e.preventDefault(),e.stopPropagation(),f({type:0}),v.nextFrame(()=>f({type:2,focus:R.First}));break;case M.R.ArrowUp:e.preventDefault(),e.stopPropagation(),f({type:0}),v.nextFrame(()=>f({type:2,focus:R.Last}))}}),y=(0,s.z)(e=>{e.key===M.R.Space&&e.preventDefault()}),b=(0,s.z)(t=>{if((0,w.P)(t.currentTarget))return t.preventDefault();e.disabled||(0===l.menuState?(f({type:1}),v.nextFrame(()=>{var e;return null==(e=l.buttonRef.current)?void 0:e.focus({preventScroll:!0})})):(t.preventDefault(),f({type:0})))}),E=(0,a.useMemo)(()=>({open:0===l.menuState}),[l]),P={ref:m,id:o,type:function(e,t){let[n,r]=(0,a.useState)(()=>p(e));return(0,d.e)(()=>{r(p(e))},[e.type,e.as]),(0,d.e)(()=>{n||t.current&&t.current instanceof HTMLButtonElement&&!t.current.hasAttribute("type")&&r("button")},[n,t]),n}(e,l.buttonRef),"aria-haspopup":"menu","aria-controls":null==(n=l.itemsRef.current)?void 0:n.id,"aria-expanded":0===l.menuState,onKeyDown:g,onKeyUp:y,onClick:b};return(0,O.sY)({ourProps:P,theirProps:i,slot:E,defaultTag:"button",name:"Menu.Button"})}),Items:(0,O.yV)(function(e,t){var n,r;let o=(0,c.M)(),{id:i=`headlessui-menu-items-${o}`,...l}=e,[f,p]=A("Menu.Items"),v=(0,h.T)(f.itemsRef,t),g=(0,m.i)(f.itemsRef),y=(0,u.G)(),w=(0,E.oJ)(),T=null!==w?(w&E.ZM.Open)===E.ZM.Open:0===f.menuState;(0,a.useEffect)(()=>{let e=f.itemsRef.current;e&&0===f.menuState&&e!==(null==g?void 0:g.activeElement)&&e.focus({preventScroll:!0})},[f.menuState,f.itemsRef,g]),function({container:e,accept:t,walk:n,enabled:r=!0}){let o=(0,a.useRef)(t),i=(0,a.useRef)(n);(0,a.useEffect)(()=>{o.current=t,i.current=n},[t,n]),(0,d.e)(()=>{if(!e||!r)return;let t=(0,b.r)(e);if(!t)return;let n=o.current,l=i.current,a=Object.assign(e=>n(e),{acceptNode:n}),u=t.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,a,!1);for(;u.nextNode();)l(u.currentNode)},[e,r,o,i])}({container:f.itemsRef.current,enabled:0===f.menuState,accept:e=>"menuitem"===e.getAttribute("role")?NodeFilter.FILTER_REJECT:e.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT,walk(e){e.setAttribute("role","none")}});let x=(0,s.z)(e=>{var t,n;switch(y.dispose(),e.key){case M.R.Space:if(""!==f.searchQuery)return e.preventDefault(),e.stopPropagation(),p({type:3,value:e.key});case M.R.Enter:if(e.preventDefault(),e.stopPropagation(),p({type:1}),null!==f.activeItemIndex){let{dataRef:e}=f.items[f.activeItemIndex];null==(n=null==(t=e.current)?void 0:t.domRef.current)||n.click()}(0,S.wI)(f.buttonRef.current);break;case M.R.ArrowDown:return e.preventDefault(),e.stopPropagation(),p({type:2,focus:R.Next});case M.R.ArrowUp:return e.preventDefault(),e.stopPropagation(),p({type:2,focus:R.Previous});case M.R.Home:case M.R.PageUp:return e.preventDefault(),e.stopPropagation(),p({type:2,focus:R.First});case M.R.End:case M.R.PageDown:return e.preventDefault(),e.stopPropagation(),p({type:2,focus:R.Last});case M.R.Escape:e.preventDefault(),e.stopPropagation(),p({type:1}),(0,P.k)().nextFrame(()=>{var e;return null==(e=f.buttonRef.current)?void 0:e.focus({preventScroll:!0})});break;case M.R.Tab:e.preventDefault(),e.stopPropagation(),p({type:1}),(0,P.k)().nextFrame(()=>{(0,S.EO)(f.buttonRef.current,e.shiftKey?S.TO.Previous:S.TO.Next)});break;default:1===e.key.length&&(p({type:3,value:e.key}),y.setTimeout(()=>p({type:4}),350))}}),C=(0,s.z)(e=>{e.key===M.R.Space&&e.preventDefault()}),k=(0,a.useMemo)(()=>({open:0===f.menuState}),[f]),L={"aria-activedescendant":null===f.activeItemIndex||null==(n=f.items[f.activeItemIndex])?void 0:n.id,"aria-labelledby":null==(r=f.buttonRef.current)?void 0:r.id,id:i,onKeyDown:x,onKeyUp:C,role:"menu",tabIndex:0,ref:v};return(0,O.sY)({ourProps:L,theirProps:l,slot:k,defaultTag:"div",features:_,visible:T,name:"Menu.Items"})}),Item:(0,O.yV)(function(e,t){let n,r,o,i=(0,c.M)(),{id:l=`headlessui-menu-item-${i}`,disabled:u=!1,...f}=e,[m,p]=A("Menu.Item"),v=null!==m.activeItemIndex&&m.items[m.activeItemIndex].id===l,b=(0,a.useRef)(null),E=(0,h.T)(t,b);(0,d.e)(()=>{if(m.__demoMode||0!==m.menuState||!v||0===m.activationTrigger)return;let e=(0,P.k)();return e.requestAnimationFrame(()=>{var e,t;null==(t=null==(e=b.current)?void 0:e.scrollIntoView)||t.call(e,{block:"nearest"})}),e.dispose},[m.__demoMode,b,v,m.menuState,m.activationTrigger,m.activeItemIndex]);let w=(n=(0,a.useRef)(""),r=(0,a.useRef)(""),(0,s.z)(()=>{let e=b.current;if(!e)return"";let t=e.innerText;if(n.current===t)return r.current;let o=(function(e){let t=e.getAttribute("aria-label");if("string"==typeof t)return t.trim();let n=e.getAttribute("aria-labelledby");if(n){let e=n.split(" ").map(e=>{let t=document.getElementById(e);if(t){let e=t.getAttribute("aria-label");return"string"==typeof e?e.trim():g(t).trim()}return null}).filter(Boolean);if(e.length>0)return e.join(", ")}return g(e).trim()})(e).trim().toLowerCase();return n.current=t,r.current=o,o})),T=(0,a.useRef)({disabled:u,domRef:b,get textValue(){return w()}});(0,d.e)(()=>{T.current.disabled=u},[T,u]),(0,d.e)(()=>(p({type:5,id:l,dataRef:T}),()=>p({type:6,id:l})),[T,l]);let M=(0,s.z)(()=>{p({type:1})}),x=(0,s.z)(e=>{if(u)return e.preventDefault();p({type:1}),(0,S.wI)(m.buttonRef.current)}),C=(0,s.z)(()=>{if(u)return p({type:2,focus:R.Nothing});p({type:2,focus:R.Specific,id:l})}),k=(o=(0,a.useRef)([-1,-1]),{wasMoved(e){let t=y(e);return(o.current[0]!==t[0]||o.current[1]!==t[1])&&(o.current=t,!0)},update(e){o.current=y(e)}}),L=(0,s.z)(e=>k.update(e)),N=(0,s.z)(e=>{k.wasMoved(e)&&(u||v||p({type:2,focus:R.Specific,id:l,trigger:0}))}),I=(0,s.z)(e=>{k.wasMoved(e)&&(u||v&&p({type:2,focus:R.Nothing}))}),j=(0,a.useMemo)(()=>({active:v,disabled:u,close:M}),[v,u,M]);return(0,O.sY)({ourProps:{id:l,ref:E,role:"menuitem",tabIndex:!0===u?void 0:-1,"aria-disabled":!0===u||void 0,disabled:void 0,onClick:x,onFocus:C,onPointerEnter:L,onMouseEnter:L,onPointerMove:N,onMouseMove:N,onPointerLeave:I,onMouseLeave:I},theirProps:f,slot:j,defaultTag:D,name:"Menu.Item"})})})},9805:function(e,t,n){"use strict";n.d(t,{u:function(){return N}});var r,o=n(2265),i=n(2769),l=n(2950),a=n(634),u=n(2600),s=n(1858),c=n(8957),d=n(6618),f=n(5390),m=n(597);function p(e,...t){e&&t.length>0&&e.classList.add(...t)}function h(e,...t){e&&t.length>0&&e.classList.remove(...t)}var v=n(5306),g=n(3960),y=n(1931);function b(e=""){return e.split(/\s+/).filter(e=>e.length>1)}let E=(0,o.createContext)(null);E.displayName="TransitionContext";var w=((r=w||{}).Visible="visible",r.Hidden="hidden",r);let R=(0,o.createContext)(null);function P(e){return"children"in e?P(e.children):e.current.filter(({el:e})=>null!==e.current).filter(({state:e})=>"visible"===e).length>0}function S(e,t){let n=(0,s.E)(e),r=(0,o.useRef)([]),u=(0,a.t)(),c=(0,i.G)(),d=(0,l.z)((e,t=y.l4.Hidden)=>{let o=r.current.findIndex(({el:t})=>t===e);-1!==o&&((0,m.E)(t,{[y.l4.Unmount](){r.current.splice(o,1)},[y.l4.Hidden](){r.current[o].state="hidden"}}),c.microTask(()=>{var e;!P(r)&&u.current&&(null==(e=n.current)||e.call(n))}))}),f=(0,l.z)(e=>{let t=r.current.find(({el:t})=>t===e);return t?"visible"!==t.state&&(t.state="visible"):r.current.push({el:e,state:"visible"}),()=>d(e,y.l4.Unmount)}),p=(0,o.useRef)([]),h=(0,o.useRef)(Promise.resolve()),v=(0,o.useRef)({enter:[],leave:[],idle:[]}),g=(0,l.z)((e,n,r)=>{p.current.splice(0),t&&(t.chains.current[n]=t.chains.current[n].filter(([t])=>t!==e)),null==t||t.chains.current[n].push([e,new Promise(e=>{p.current.push(e)})]),null==t||t.chains.current[n].push([e,new Promise(e=>{Promise.all(v.current[n].map(([e,t])=>t)).then(()=>e())})]),"enter"===n?h.current=h.current.then(()=>null==t?void 0:t.wait.current).then(()=>r(n)):r(n)}),b=(0,l.z)((e,t,n)=>{Promise.all(v.current[t].splice(0).map(([e,t])=>t)).then(()=>{var e;null==(e=p.current.shift())||e()}).then(()=>n(t))});return(0,o.useMemo)(()=>({children:r,register:f,unregister:d,onStart:g,onStop:b,wait:h,chains:v}),[f,d,r,g,b,v,h])}function T(){}R.displayName="NestingContext";let O=["beforeEnter","afterEnter","beforeLeave","afterLeave"];function M(e){var t;let n={};for(let r of O)n[r]=null!=(t=e[r])?t:T;return n}let x=y.AN.RenderStrategy,C=(0,y.yV)(function(e,t){let{show:n,appear:r=!1,unmount:i=!0,...a}=e,s=(0,o.useRef)(null),f=(0,d.T)(s,t);(0,c.H)();let m=(0,v.oJ)();if(void 0===n&&null!==m&&(n=(m&v.ZM.Open)===v.ZM.Open),![!0,!1].includes(n))throw Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");let[p,h]=(0,o.useState)(n?"visible":"hidden"),g=S(()=>{h("hidden")}),[b,w]=(0,o.useState)(!0),T=(0,o.useRef)([n]);(0,u.e)(()=>{!1!==b&&T.current[T.current.length-1]!==n&&(T.current.push(n),w(!1))},[T,n]);let O=(0,o.useMemo)(()=>({show:n,appear:r,initial:b}),[n,r,b]);(0,o.useEffect)(()=>{if(n)h("visible");else if(P(g)){let e=s.current;if(!e)return;let t=e.getBoundingClientRect();0===t.x&&0===t.y&&0===t.width&&0===t.height&&h("hidden")}else h("hidden")},[n,g]);let M={unmount:i},C=(0,l.z)(()=>{var t;b&&w(!1),null==(t=e.beforeEnter)||t.call(e)}),L=(0,l.z)(()=>{var t;b&&w(!1),null==(t=e.beforeLeave)||t.call(e)});return o.createElement(R.Provider,{value:g},o.createElement(E.Provider,{value:O},(0,y.sY)({ourProps:{...M,as:o.Fragment,children:o.createElement(k,{ref:f,...M,...a,beforeEnter:C,beforeLeave:L})},theirProps:{},defaultTag:o.Fragment,features:x,visible:"visible"===p,name:"Transition"})))}),k=(0,y.yV)(function(e,t){var n,r,w;let T;let{beforeEnter:O,afterEnter:C,beforeLeave:k,afterLeave:L,enter:N,enterFrom:I,enterTo:A,entered:j,leave:F,leaveFrom:_,leaveTo:D,...H}=e,$=(0,o.useRef)(null),U=(0,d.T)($,t),W=null==(n=H.unmount)||n?y.l4.Unmount:y.l4.Hidden,{show:z,appear:V,initial:B}=function(){let e=(0,o.useContext)(E);if(null===e)throw Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),[Z,Y]=(0,o.useState)(z?"visible":"hidden"),K=function(){let e=(0,o.useContext)(R);if(null===e)throw Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),{register:q,unregister:G}=K;(0,o.useEffect)(()=>q($),[q,$]),(0,o.useEffect)(()=>{if(W===y.l4.Hidden&&$.current){if(z&&"visible"!==Z){Y("visible");return}return(0,m.E)(Z,{hidden:()=>G($),visible:()=>q($)})}},[Z,$,q,G,z,W]);let Q=(0,s.E)({base:b(H.className),enter:b(N),enterFrom:b(I),enterTo:b(A),entered:b(j),leave:b(F),leaveFrom:b(_),leaveTo:b(D)}),J=(w={beforeEnter:O,afterEnter:C,beforeLeave:k,afterLeave:L},T=(0,o.useRef)(M(w)),(0,o.useEffect)(()=>{T.current=M(w)},[w]),T),X=(0,c.H)();(0,o.useEffect)(()=>{if(X&&"visible"===Z&&null===$.current)throw Error("Did you forget to passthrough the `ref` to the actual DOM node?")},[$,Z,X]);let ee=V&&z&&B,et=X&&(!B||V)?z?"enter":"leave":"idle",en=function(e=0){let[t,n]=(0,o.useState)(e),r=(0,a.t)(),i=(0,o.useCallback)(e=>{r.current&&n(t=>t|e)},[t,r]),l=(0,o.useCallback)(e=>!!(t&e),[t]);return{flags:t,addFlag:i,hasFlag:l,removeFlag:(0,o.useCallback)(e=>{r.current&&n(t=>t&~e)},[n,r]),toggleFlag:(0,o.useCallback)(e=>{r.current&&n(t=>t^e)},[n])}}(0),er=(0,l.z)(e=>(0,m.E)(e,{enter:()=>{en.addFlag(v.ZM.Opening),J.current.beforeEnter()},leave:()=>{en.addFlag(v.ZM.Closing),J.current.beforeLeave()},idle:()=>{}})),eo=(0,l.z)(e=>(0,m.E)(e,{enter:()=>{en.removeFlag(v.ZM.Opening),J.current.afterEnter()},leave:()=>{en.removeFlag(v.ZM.Closing),J.current.afterLeave()},idle:()=>{}})),ei=S(()=>{Y("hidden"),G($)},K),el=(0,o.useRef)(!1);!function({immediate:e,container:t,direction:n,classes:r,onStart:o,onStop:l}){let c=(0,a.t)(),d=(0,i.G)(),v=(0,s.E)(n);(0,u.e)(()=>{e&&(v.current="enter")},[e]),(0,u.e)(()=>{let e=(0,f.k)();d.add(e.dispose);let n=t.current;if(n&&"idle"!==v.current&&c.current){var i,a,u;let t,s,c,d,g,y,b;return e.dispose(),o.current(v.current),e.add((i=r.current,a="enter"===v.current,u=()=>{e.dispose(),l.current(v.current)},s=a?"enter":"leave",c=(0,f.k)(),d=void 0!==u?(t={called:!1},(...e)=>{if(!t.called)return t.called=!0,u(...e)}):()=>{},"enter"===s&&(n.removeAttribute("hidden"),n.style.display=""),g=(0,m.E)(s,{enter:()=>i.enter,leave:()=>i.leave}),y=(0,m.E)(s,{enter:()=>i.enterTo,leave:()=>i.leaveTo}),b=(0,m.E)(s,{enter:()=>i.enterFrom,leave:()=>i.leaveFrom}),h(n,...i.base,...i.enter,...i.enterTo,...i.enterFrom,...i.leave,...i.leaveFrom,...i.leaveTo,...i.entered),p(n,...i.base,...g,...b),c.nextFrame(()=>{h(n,...i.base,...g,...b),p(n,...i.base,...g,...y),function(e,t){let n=(0,f.k)();if(!e)return n.dispose;let{transitionDuration:r,transitionDelay:o}=getComputedStyle(e),[i,l]=[r,o].map(e=>{let[t=0]=e.split(",").filter(Boolean).map(e=>e.includes("ms")?parseFloat(e):1e3*parseFloat(e)).sort((e,t)=>t-e);return t}),a=i+l;if(0!==a){n.group(n=>{n.setTimeout(()=>{t(),n.dispose()},a),n.addEventListener(e,"transitionrun",e=>{e.target===e.currentTarget&&n.dispose()})});let r=n.addEventListener(e,"transitionend",e=>{e.target===e.currentTarget&&(t(),r())})}else t();n.add(()=>t()),n.dispose}(n,()=>(h(n,...i.base,...g),p(n,...i.base,...i.entered),d()))}),c.dispose)),e.dispose}},[n])}({immediate:ee,container:$,classes:Q,direction:et,onStart:(0,s.E)(e=>{el.current=!0,ei.onStart($,e,er)}),onStop:(0,s.E)(e=>{el.current=!1,ei.onStop($,e,eo),"leave"!==e||P(ei)||(Y("hidden"),G($))})});let ea=H;return ee?ea={...ea,className:(0,g.A)(H.className,...Q.current.enter,...Q.current.enterFrom)}:el.current&&(ea.className=(0,g.A)(H.className,null==(r=$.current)?void 0:r.className),""===ea.className&&delete ea.className),o.createElement(R.Provider,{value:ei},o.createElement(v.up,{value:(0,m.E)(Z,{visible:v.ZM.Open,hidden:v.ZM.Closed})|en.flags},(0,y.sY)({ourProps:{ref:U},theirProps:ea,defaultTag:"div",features:x,visible:"visible"===Z,name:"Transition.Child"})))}),L=(0,y.yV)(function(e,t){let n=null!==(0,o.useContext)(E),r=null!==(0,v.oJ)();return o.createElement(o.Fragment,null,!n&&r?o.createElement(C,{ref:t,...e}):o.createElement(k,{ref:t,...e}))}),N=Object.assign(C,{Child:L,Root:C})},2769:function(e,t,n){"use strict";n.d(t,{G:function(){return i}});var r=n(2265),o=n(5390);function i(){let[e]=(0,r.useState)(o.k);return(0,r.useEffect)(()=>()=>e.dispose(),[e]),e}},2950:function(e,t,n){"use strict";n.d(t,{z:function(){return i}});var r=n(2265),o=n(1858);let i=function(e){let t=(0,o.E)(e);return r.useCallback((...e)=>t.current(...e),[t])}},5606:function(e,t,n){"use strict";n.d(t,{M:function(){return u}});var r,o=n(2265),i=n(2057),l=n(2600),a=n(8957);let u=null!=(r=o.useId)?r:function(){let e=(0,a.H)(),[t,n]=o.useState(e?()=>i.O.nextId():null);return(0,l.e)(()=>{null===t&&n(i.O.nextId())},[t]),null!=t?""+t:void 0}},634:function(e,t,n){"use strict";n.d(t,{t:function(){return i}});var r=n(2265),o=n(2600);function i(){let e=(0,r.useRef)(!1);return(0,o.e)(()=>(e.current=!0,()=>{e.current=!1}),[]),e}},2600:function(e,t,n){"use strict";n.d(t,{e:function(){return i}});var r=n(2265),o=n(2057);let i=(e,t)=>{o.O.isServer?(0,r.useEffect)(e,t):(0,r.useLayoutEffect)(e,t)}},1858:function(e,t,n){"use strict";n.d(t,{E:function(){return i}});var r=n(2265),o=n(2600);function i(e){let t=(0,r.useRef)(e);return(0,o.e)(()=>{t.current=e},[e]),t}},583:function(e,t,n){"use strict";n.d(t,{O:function(){return s}});var r=n(2265),o=n(5410),i=n(4644),l=n(1858);function a(e,t,n){let o=(0,l.E)(t);(0,r.useEffect)(()=>{function t(e){o.current(e)}return document.addEventListener(e,t,n),()=>document.removeEventListener(e,t,n)},[e,n])}var u=n(7976);function s(e,t,n=!0){let l=(0,r.useRef)(!1);function s(n,r){if(!l.current||n.defaultPrevented)return;let i=r(n);if(null!==i&&i.getRootNode().contains(i)&&i.isConnected){for(let t of function e(t){return"function"==typeof t?e(t()):Array.isArray(t)||t instanceof Set?t:[t]}(e)){if(null===t)continue;let e=t instanceof HTMLElement?t:t.current;if(null!=e&&e.contains(i)||n.composed&&n.composedPath().includes(e))return}return(0,o.sP)(i,o.tJ.Loose)||-1===i.tabIndex||n.preventDefault(),t(n,i)}}(0,r.useEffect)(()=>{requestAnimationFrame(()=>{l.current=n})},[n]);let c=(0,r.useRef)(null);a("pointerdown",e=>{var t,n;l.current&&(c.current=(null==(n=null==(t=e.composedPath)?void 0:t.call(e))?void 0:n[0])||e.target)},!0),a("mousedown",e=>{var t,n;l.current&&(c.current=(null==(n=null==(t=e.composedPath)?void 0:t.call(e))?void 0:n[0])||e.target)},!0),a("click",e=>{(0,i.tq)()||c.current&&(s(e,()=>c.current),c.current=null)},!0),a("touchend",e=>s(e,()=>e.target instanceof HTMLElement?e.target:null),!0),(0,u.s)("blur",e=>s(e,()=>window.document.activeElement instanceof HTMLIFrameElement?window.document.activeElement:null),!0)}},9888:function(e,t,n){"use strict";n.d(t,{i:function(){return i}});var r=n(2265),o=n(4851);function i(...e){return(0,r.useMemo)(()=>(0,o.r)(...e),[...e])}},8957:function(e,t,n){"use strict";n.d(t,{H:function(){return l}});var r,o=n(2265),i=n(2057);function l(){let e;let t=(e="undefined"==typeof document,(0,(r||(r=n.t(o,2))).useSyncExternalStore)(()=>()=>{},()=>!1,()=>!e)),[l,a]=o.useState(i.O.isHandoffComplete);return l&&!1===i.O.isHandoffComplete&&a(!1),o.useEffect(()=>{!0!==l&&a(!0)},[l]),o.useEffect(()=>i.O.handoff(),[]),!t&&l}},6618:function(e,t,n){"use strict";n.d(t,{T:function(){return a},h:function(){return l}});var r=n(2265),o=n(2950);let i=Symbol();function l(e,t=!0){return Object.assign(e,{[i]:t})}function a(...e){let t=(0,r.useRef)(e);(0,r.useEffect)(()=>{t.current=e},[e]);let n=(0,o.z)(e=>{for(let n of t.current)null!=n&&("function"==typeof n?n(e):n.current=e)});return e.every(e=>null==e||(null==e?void 0:e[i]))?void 0:n}},7976:function(e,t,n){"use strict";n.d(t,{s:function(){return i}});var r=n(2265),o=n(1858);function i(e,t,n){let i=(0,o.E)(t);(0,r.useEffect)(()=>{function t(e){i.current(e)}return window.addEventListener(e,t,n),()=>window.removeEventListener(e,t,n)},[e,n])}},5306:function(e,t,n){"use strict";n.d(t,{ZM:function(){return l},oJ:function(){return a},up:function(){return u}});var r,o=n(2265);let i=(0,o.createContext)(null);i.displayName="OpenClosedContext";var l=((r=l||{})[r.Open=1]="Open",r[r.Closed=2]="Closed",r[r.Closing=4]="Closing",r[r.Opening=8]="Opening",r);function a(){return(0,o.useContext)(i)}function u({value:e,children:t}){return o.createElement(i.Provider,{value:e},t)}},5863:function(e,t,n){"use strict";function r(e){let t=e.parentElement,n=null;for(;t&&!(t instanceof HTMLFieldSetElement);)t instanceof HTMLLegendElement&&(n=t),t=t.parentElement;let r=(null==t?void 0:t.getAttribute("disabled"))==="";return!(r&&function(e){if(!e)return!1;let t=e.previousElementSibling;for(;null!==t;){if(t instanceof HTMLLegendElement)return!1;t=t.previousElementSibling}return!0}(n))&&r}n.d(t,{P:function(){return r}})},3960:function(e,t,n){"use strict";function r(...e){return Array.from(new Set(e.flatMap(e=>"string"==typeof e?e.split(" "):[]))).filter(Boolean).join(" ")}n.d(t,{A:function(){return r}})},5390:function(e,t,n){"use strict";n.d(t,{k:function(){return function e(){let t=[],n={addEventListener:(e,t,r,o)=>(e.addEventListener(t,r,o),n.add(()=>e.removeEventListener(t,r,o))),requestAnimationFrame(...e){let t=requestAnimationFrame(...e);return n.add(()=>cancelAnimationFrame(t))},nextFrame:(...e)=>n.requestAnimationFrame(()=>n.requestAnimationFrame(...e)),setTimeout(...e){let t=setTimeout(...e);return n.add(()=>clearTimeout(t))},microTask(...e){let t={current:!0};return(0,r.Y)(()=>{t.current&&e[0]()}),n.add(()=>{t.current=!1})},style(e,t,n){let r=e.style.getPropertyValue(t);return Object.assign(e.style,{[t]:n}),this.add(()=>{Object.assign(e.style,{[t]:r})})},group(t){let n=e();return t(n),this.add(()=>n.dispose())},add:e=>(t.push(e),()=>{let n=t.indexOf(e);if(n>=0)for(let e of t.splice(n,1))e()}),dispose(){for(let e of t.splice(0))e()}};return n}}});var r=n(5195)},2057:function(e,t,n){"use strict";n.d(t,{O:function(){return a}});var r=Object.defineProperty,o=(e,t,n)=>t in e?r(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,i=(e,t,n)=>(o(e,"symbol"!=typeof t?t+"":t,n),n);class l{constructor(){i(this,"current",this.detect()),i(this,"handoffState","pending"),i(this,"currentId",0)}set(e){this.current!==e&&(this.handoffState="pending",this.currentId=0,this.current=e)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return"server"===this.current}get isClient(){return"client"===this.current}detect(){return"undefined"==typeof window||"undefined"==typeof document?"server":"client"}handoff(){"pending"===this.handoffState&&(this.handoffState="complete")}get isHandoffComplete(){return"complete"===this.handoffState}}let a=new l},5410:function(e,t,n){"use strict";n.d(t,{C5:function(){return E},EO:function(){return R},TO:function(){return f},fE:function(){return m},jA:function(){return P},sP:function(){return g},tJ:function(){return v},wI:function(){return y},z2:function(){return w}});var r,o,i,l,a,u=n(5390),s=n(597),c=n(4851);let d=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map(e=>`${e}:not([tabindex='-1'])`).join(",");var f=((r=f||{})[r.First=1]="First",r[r.Previous=2]="Previous",r[r.Next=4]="Next",r[r.Last=8]="Last",r[r.WrapAround=16]="WrapAround",r[r.NoScroll=32]="NoScroll",r),m=((o=m||{})[o.Error=0]="Error",o[o.Overflow=1]="Overflow",o[o.Success=2]="Success",o[o.Underflow=3]="Underflow",o),p=((i=p||{})[i.Previous=-1]="Previous",i[i.Next=1]="Next",i);function h(e=document.body){return null==e?[]:Array.from(e.querySelectorAll(d)).sort((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}var v=((l=v||{})[l.Strict=0]="Strict",l[l.Loose=1]="Loose",l);function g(e,t=0){var n;return e!==(null==(n=(0,c.r)(e))?void 0:n.body)&&(0,s.E)(t,{0:()=>e.matches(d),1(){let t=e;for(;null!==t;){if(t.matches(d))return!0;t=t.parentElement}return!1}})}function y(e){let t=(0,c.r)(e);(0,u.k)().nextFrame(()=>{t&&!g(t.activeElement,0)&&E(e)})}var b=((a=b||{})[a.Keyboard=0]="Keyboard",a[a.Mouse=1]="Mouse",a);function E(e){null==e||e.focus({preventScroll:!0})}function w(e,t=e=>e){return e.slice().sort((e,n)=>{let r=t(e),o=t(n);if(null===r||null===o)return 0;let i=r.compareDocumentPosition(o);return i&Node.DOCUMENT_POSITION_FOLLOWING?-1:i&Node.DOCUMENT_POSITION_PRECEDING?1:0})}function R(e,t){return P(h(),t,{relativeTo:e})}function P(e,t,{sorted:n=!0,relativeTo:r=null,skipElements:o=[]}={}){var i,l,a;let u=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,s=Array.isArray(e)?n?w(e):e:h(e);o.length>0&&s.length>1&&(s=s.filter(e=>!o.includes(e))),r=null!=r?r:u.activeElement;let c=(()=>{if(5&t)return 1;if(10&t)return -1;throw Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),d=(()=>{if(1&t)return 0;if(2&t)return Math.max(0,s.indexOf(r))-1;if(4&t)return Math.max(0,s.indexOf(r))+1;if(8&t)return s.length-1;throw Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),f=32&t?{preventScroll:!0}:{},m=0,p=s.length,v;do{if(m>=p||m+p<=0)return 0;let e=d+m;if(16&t)e=(e+p)%p;else{if(e<0)return 3;if(e>=p)return 1}null==(v=s[e])||v.focus(f),m+=c}while(v!==u.activeElement);return 6&t&&null!=(a=null==(l=null==(i=v)?void 0:i.matches)?void 0:l.call(i,"textarea,input"))&&a&&v.select(),2}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("keydown",e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible="")},!0),document.addEventListener("click",e=>{1===e.detail?delete document.documentElement.dataset.headlessuiFocusVisible:0===e.detail&&(document.documentElement.dataset.headlessuiFocusVisible="")},!0))},597:function(e,t,n){"use strict";function r(e,t,...n){if(e in t){let r=t[e];return"function"==typeof r?r(...n):r}let o=Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(t).map(e=>`"${e}"`).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(o,r),o}n.d(t,{E:function(){return r}})},5195:function(e,t,n){"use strict";function r(e){"function"==typeof queueMicrotask?queueMicrotask(e):Promise.resolve().then(e).catch(e=>setTimeout(()=>{throw e}))}n.d(t,{Y:function(){return r}})},4851:function(e,t,n){"use strict";n.d(t,{r:function(){return o}});var r=n(2057);function o(e){return r.O.isServer?null:e instanceof Node?e.ownerDocument:null!=e&&e.hasOwnProperty("current")&&e.current instanceof Node?e.current.ownerDocument:document}},4644:function(e,t,n){"use strict";function r(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function o(){return r()||/Android/gi.test(window.navigator.userAgent)}n.d(t,{gn:function(){return r},tq:function(){return o}})},1931:function(e,t,n){"use strict";n.d(t,{AN:function(){return u},l4:function(){return s},sY:function(){return c},yV:function(){return p}});var r,o,i=n(2265),l=n(3960),a=n(597),u=((r=u||{})[r.None=0]="None",r[r.RenderStrategy=1]="RenderStrategy",r[r.Static=2]="Static",r),s=((o=s||{})[o.Unmount=0]="Unmount",o[o.Hidden=1]="Hidden",o);function c({ourProps:e,theirProps:t,slot:n,defaultTag:r,features:o,visible:i=!0,name:l,mergeRefs:u}){u=null!=u?u:f;let s=m(t,e);if(i)return d(s,n,r,l,u);let c=null!=o?o:0;if(2&c){let{static:e=!1,...t}=s;if(e)return d(t,n,r,l,u)}if(1&c){let{unmount:e=!0,...t}=s;return(0,a.E)(e?0:1,{0:()=>null,1:()=>d({...t,hidden:!0,style:{display:"none"}},n,r,l,u)})}return d(s,n,r,l,u)}function d(e,t={},n,r,o){let{as:a=n,children:u,refName:s="ref",...c}=v(e,["unmount","static"]),d=void 0!==e.ref?{[s]:e.ref}:{},f="function"==typeof u?u(t):u;"className"in c&&c.className&&"function"==typeof c.className&&(c.className=c.className(t));let p={};if(t){let e=!1,n=[];for(let[r,o]of Object.entries(t))"boolean"==typeof o&&(e=!0),!0===o&&n.push(r);e&&(p["data-headlessui-state"]=n.join(" "))}if(a===i.Fragment&&Object.keys(h(c)).length>0){if(!(0,i.isValidElement)(f)||Array.isArray(f)&&f.length>1)throw Error(['Passing props on "Fragment"!',"",`The current component <${r} /> is rendering a "Fragment".`,"However we need to passthrough the following props:",Object.keys(c).map(e=>`  - ${e}`).join(`
`),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map(e=>`  - ${e}`).join(`
`)].join(`
`));let e=f.props,t="function"==typeof(null==e?void 0:e.className)?(...t)=>(0,l.A)(null==e?void 0:e.className(...t),c.className):(0,l.A)(null==e?void 0:e.className,c.className);return(0,i.cloneElement)(f,Object.assign({},m(f.props,h(v(c,["ref"]))),p,d,{ref:o(f.ref,d.ref)},t?{className:t}:{}))}return(0,i.createElement)(a,Object.assign({},v(c,["ref"]),a!==i.Fragment&&d,a!==i.Fragment&&p),f)}function f(...e){return e.every(e=>null==e)?void 0:t=>{for(let n of e)null!=n&&("function"==typeof n?n(t):n.current=t)}}function m(...e){if(0===e.length)return{};if(1===e.length)return e[0];let t={},n={};for(let r of e)for(let e in r)e.startsWith("on")&&"function"==typeof r[e]?(null!=n[e]||(n[e]=[]),n[e].push(r[e])):t[e]=r[e];if(t.disabled||t["aria-disabled"])return Object.assign(t,Object.fromEntries(Object.keys(n).map(e=>[e,void 0])));for(let e in n)Object.assign(t,{[e](t,...r){for(let o of n[e]){if((t instanceof Event||(null==t?void 0:t.nativeEvent)instanceof Event)&&t.defaultPrevented)return;o(t,...r)}}});return t}function p(e){var t;return Object.assign((0,i.forwardRef)(e),{displayName:null!=(t=e.displayName)?t:e.name})}function h(e){let t=Object.assign({},e);for(let e in t)void 0===t[e]&&delete t[e];return t}function v(e,t=[]){let n=Object.assign({},e);for(let e of t)e in n&&delete n[e];return n}},7865:function(e,t,n){"use strict";var r=n(2265);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"}))});t.Z=o},1861:function(e,t,n){"use strict";var r=n(2265);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"}))});t.Z=o},3339:function(e,t,n){"use strict";var r=n(2265);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M20.25 14.15v4.25c0 1.094-.787 2.036-1.872 2.18-2.087.277-4.216.42-6.378.42s-4.291-.143-6.378-.42c-1.085-.144-1.872-1.086-1.872-2.18v-4.25m16.5 0a2.18 2.18 0 0 0 .75-1.661V8.706c0-1.081-.768-2.015-1.837-2.175a48.114 48.114 0 0 0-3.413-.387m4.5 8.006c-.194.165-.42.295-.673.38A23.978 23.978 0 0 1 12 15.75c-2.648 0-5.195-.429-7.577-1.22a2.016 2.016 0 0 1-.673-.38m0 0A2.18 2.18 0 0 1 3 12.489V8.706c0-1.081.768-2.015 1.837-2.175a48.111 48.111 0 0 1 3.413-.387m7.5 0V5.25A2.25 2.25 0 0 0 13.5 3h-3a2.25 2.25 0 0 0-2.25 2.25v.894m7.5 0a48.667 48.667 0 0 0-7.5 0M12 12.75h.008v.008H12v-.008Z"}))});t.Z=o},6233:function(e,t,n){"use strict";var r=n(2265);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5"}))});t.Z=o},8447:function(e,t,n){"use strict";var r=n(2265);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"}))});t.Z=o},9776:function(e,t,n){"use strict";var r=n(2265);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 0 1-.825-.242m9.345-8.334a2.126 2.126 0 0 0-.476-.095 48.64 48.64 0 0 0-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0 0 11.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155"}))});t.Z=o},7714:function(e,t,n){"use strict";var r=n(2265);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.5 12a7.5 7.5 0 0 0 15 0m-15 0a7.5 7.5 0 1 1 15 0m-15 0H3m16.5 0H21m-1.5 0H12m-8.457 3.077 1.41-.513m14.095-5.13 1.41-.513M5.106 17.785l1.15-.964m11.49-9.642 1.149-.964M7.501 19.795l.75-1.3m7.5-12.99.75-1.3m-6.063 16.658.26-1.477m2.605-14.772.26-1.477m0 17.726-.26-1.477M10.698 4.614l-.26-1.477M16.5 19.794l-.75-1.299M7.5 4.205 12 12m6.894 5.785-1.149-.964M6.256 7.178l-1.15-.964m15.352 8.864-1.41-.513M4.954 9.435l-1.41-.514M12.002 12l-3.75 6.495"}))});t.Z=o},4697:function(e,t,n){"use strict";var r=n(2265);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))});t.Z=o},5372:function(e,t,n){"use strict";var r=n(2265);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"}))});t.Z=o},8362:function(e,t,n){"use strict";var r=n(2265);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.752 15.002A9.72 9.72 0 0 1 18 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 0 0 3 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 0 0 9.002-5.998Z"}))});t.Z=o},8657:function(e,t,n){"use strict";var r=n(2265);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 3v2.25m6.364.386-1.591 1.591M21 12h-2.25m-.386 6.364-1.591-1.591M12 18.75V21m-4.773-4.227-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0Z"}))});t.Z=o},7822:function(e,t,n){"use strict";var r=n(2265);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"}))});t.Z=o},3274:function(e,t,n){"use strict";var r=n(2265);let o=r.forwardRef(function({title:e,titleId:t,...n},o){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},n),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))});t.Z=o},794:function(e,t,n){"use strict";function r(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=function e(t){var n,r,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t){if(Array.isArray(t)){var i=t.length;for(n=0;n<i;n++)t[n]&&(r=e(t[n]))&&(o&&(o+=" "),o+=r)}else for(r in t)t[r]&&(o&&(o+=" "),o+=r)}return o}(e))&&(r&&(r+=" "),r+=t);return r}n.d(t,{W:function(){return r}})}}]);