'use client';

import { PlusIcon, CalendarIcon, ChatBubbleLeftRightIcon, CogIcon } from '@heroicons/react/24/outline';

const actions = [
  {
    name: 'إضافة خدمة جديدة',
    description: 'أنشئ خدمة جديدة لعرضها للعملاء',
    href: '/dashboard/services/new',
    icon: PlusIcon,
    color: 'bg-primary-500 hover:bg-primary-600',
  },
  {
    name: 'إدارة التقويم',
    description: 'تحديث أوقات العمل والتوفر',
    href: '/dashboard/calendar',
    icon: CalendarIcon,
    color: 'bg-green-500 hover:bg-green-600',
  },
  {
    name: 'الرسائل',
    description: 'تحقق من الرسائل الجديدة من العملاء',
    href: '/dashboard/messages',
    icon: ChatBubbleLeftRightIcon,
    color: 'bg-blue-500 hover:bg-blue-600',
  },
  {
    name: 'تحديث الملف الشخصي',
    description: 'تحسين ملفك الشخصي لجذب المزيد من العملاء',
    href: '/dashboard/profile',
    icon: CogIcon,
    color: 'bg-purple-500 hover:bg-purple-600',
  },
];

export function QuickActions() {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
        إجراءات سريعة
      </h3>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        {actions.map((action) => (
          <a
            key={action.name}
            href={action.href}
            className="group relative rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <div className={`flex-shrink-0 p-2 rounded-md ${action.color} transition-colors`}>
                <action.icon className="h-5 w-5 text-white" aria-hidden="true" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  {action.name}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  {action.description}
                </p>
              </div>
            </div>
          </a>
        ))}
      </div>
    </div>
  );
}
