1:HL["/_next/static/css/f53bbd662e26c898.css","style",{"crossOrigin":""}]
0:["dNEiElFYUnUoBEpXXYUk2",[[["",{"children":["dashboard",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],"$L2",[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/f53bbd662e26c898.css","precedence":"next","crossOrigin":""}]],"$L3"]]]]
4:I[9850,["854","static/chunks/854-74415fcb13f29e92.js","185","static/chunks/app/layout-d81246ab052719b7.js"],"Providers"]
5:I[6954,[],""]
6:I[7264,[],""]
7:I[4672,["91","static/chunks/91-14eaf7cc28a67c25.js","663","static/chunks/app/dashboard/layout-f8a113c0719fcd31.js"],""]
9:I[8297,[],""]
a:I[5732,["702","static/chunks/app/dashboard/page-faaf302e85cd23a5.js"],""]
b:{}
2:[null,["$","html",null,{"lang":"ar","dir":"rtl","suppressHydrationWarning":true,"children":[["$","head",null,{"children":["$","link",null,{"href":"https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap","rel":"stylesheet"}]}],["$","body",null,{"className":"__variable_e8ce0c font-arabic antialiased","children":["$","$L4",null,{"children":["$","$L5",null,{"parallelRouterKey":"children","segmentPath":["children"],"loading":"$undefined","loadingStyles":"$undefined","loadingScripts":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L6",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":"404"}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],"notFoundStyles":[],"initialChildNode":[null,["$","$L7",null,{"children":["$","$L5",null,{"parallelRouterKey":"children","segmentPath":["children","dashboard","children"],"loading":"$undefined","loadingStyles":"$undefined","loadingScripts":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L6",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","notFoundStyles":"$undefined","initialChildNode":["$L8",["$","$L9",null,{"propsForComponent":{"params":{}},"Component":"$a","isStaticGeneration":true}],null],"childPropSegment":"__PAGE__","styles":null}],"params":"$b"}],null],"childPropSegment":"dashboard","styles":null}]}]}]]}],null]
3:[["$","meta","0",{"name":"viewport","content":"width=device-width, initial-scale=1"}],["$","meta","1",{"charSet":"utf-8"}],["$","title","2",{"children":"Freela Syria - Expert Dashboard"}],["$","meta","3",{"name":"description","content":"Expert dashboard for Freela Syria marketplace"}],["$","meta","4",{"name":"author","content":"Freela Syria Team"}],["$","meta","5",{"name":"keywords","content":"freelance,syria,expert,dashboard"}]]
8:null
