
> @freela/expert-dashboard@1.0.0 build
> next build

   ▲ Next.js 14.0.3

   Creating an optimized production build ...
 ✓ Compiled successfully
   Linting and checking validity of types ...
 ⨯ ESLint: Failed to load config "@typescript-eslint/recommended" to extend from. Referenced from: C:\Users\<USER>\Documents\Freela\.eslintrc.js
   Collecting page data ...
   Generating static pages (0/5) ...
TypeError: Cannot read properties of null (reading 'useContext')
    at exports.useContext (C:\Users\<USER>\Documents\Freela\node_modules\react\cjs\react.production.min.js:24:118)
    at StyleRegistry (C:\Users\<USER>\Documents\Freela\node_modules\styled-jsx\dist\index\index.js:450:30)
    at Wc (C:\Users\<USER>\Documents\Freela\apps\expert-dashboard\node_modules\react-dom\cjs\react-dom-server.browser.production.min.js:68:44)
    at Zc (C:\Users\<USER>\Documents\Freela\apps\expert-dashboard\node_modules\react-dom\cjs\react-dom-server.browser.production.min.js:70:253)
    at Z (C:\Users\<USER>\Documents\Freela\apps\expert-dashboard\node_modules\react-dom\cjs\react-dom-server.browser.production.min.js:76:89)
    at Zc (C:\Users\<USER>\Documents\Freela\apps\expert-dashboard\node_modules\react-dom\cjs\react-dom-server.browser.production.min.js:74:209)
    at Z (C:\Users\<USER>\Documents\Freela\apps\expert-dashboard\node_modules\react-dom\cjs\react-dom-server.browser.production.min.js:76:89)
    at Zc (C:\Users\<USER>\Documents\Freela\apps\expert-dashboard\node_modules\react-dom\cjs\react-dom-server.browser.production.min.js:74:209)
    at Z (C:\Users\<USER>\Documents\Freela\apps\expert-dashboard\node_modules\react-dom\cjs\react-dom-server.browser.production.min.js:76:89)
    at Zc (C:\Users\<USER>\Documents\Freela\apps\expert-dashboard\node_modules\react-dom\cjs\react-dom-server.browser.production.min.js:74:209)

Error occurred prerendering page "/404". Read more: https://nextjs.org/docs/messages/prerender-error
TypeError: Cannot read properties of null (reading 'useContext')
    at exports.useContext (C:\Users\<USER>\Documents\Freela\node_modules\react\cjs\react.production.min.js:24:118)
    at StyleRegistry (C:\Users\<USER>\Documents\Freela\node_modules\styled-jsx\dist\index\index.js:450:30)
    at Wc (C:\Users\<USER>\Documents\Freela\apps\expert-dashboard\node_modules\react-dom\cjs\react-dom-server.browser.production.min.js:68:44)
    at Zc (C:\Users\<USER>\Documents\Freela\apps\expert-dashboard\node_modules\react-dom\cjs\react-dom-server.browser.production.min.js:70:253)
    at Z (C:\Users\<USER>\Documents\Freela\apps\expert-dashboard\node_modules\react-dom\cjs\react-dom-server.browser.production.min.js:76:89)
    at Zc (C:\Users\<USER>\Documents\Freela\apps\expert-dashboard\node_modules\react-dom\cjs\react-dom-server.browser.production.min.js:74:209)
    at Z (C:\Users\<USER>\Documents\Freela\apps\expert-dashboard\node_modules\react-dom\cjs\react-dom-server.browser.production.min.js:76:89)
    at Zc (C:\Users\<USER>\Documents\Freela\apps\expert-dashboard\node_modules\react-dom\cjs\react-dom-server.browser.production.min.js:74:209)
    at Z (C:\Users\<USER>\Documents\Freela\apps\expert-dashboard\node_modules\react-dom\cjs\react-dom-server.browser.production.min.js:76:89)
    at Zc (C:\Users\<USER>\Documents\Freela\apps\expert-dashboard\node_modules\react-dom\cjs\react-dom-server.browser.production.min.js:74:209)
TypeError: Cannot read properties of null (reading 'useContext')
    at exports.useContext (C:\Users\<USER>\Documents\Freela\node_modules\react\cjs\react.production.min.js:24:118)
    at StyleRegistry (C:\Users\<USER>\Documents\Freela\node_modules\styled-jsx\dist\index\index.js:450:30)
    at Wc (C:\Users\<USER>\Documents\Freela\apps\expert-dashboard\node_modules\react-dom\cjs\react-dom-server.browser.production.min.js:68:44)

   Generating static pages (1/5) 
    at Zc (C:\Users\<USER>\Documents\Freela\apps\expert-dashboard\node_modules\react-dom\cjs\react-dom-server.browser.production.min.js:70:253)
    at Z (C:\Users\<USER>\Documents\Freela\apps\expert-dashboard\node_modules\react-dom\cjs\react-dom-server.browser.production.min.js:76:89)
    at Zc (C:\Users\<USER>\Documents\Freela\apps\expert-dashboard\node_modules\react-dom\cjs\react-dom-server.browser.production.min.js:74:209)
    at Z (C:\Users\<USER>\Documents\Freela\apps\expert-dashboard\node_modules\react-dom\cjs\react-dom-server.browser.production.min.js:76:89)
    at Zc (C:\Users\<USER>\Documents\Freela\apps\expert-dashboard\node_modules\react-dom\cjs\react-dom-server.browser.production.min.js:74:209)
    at Z (C:\Users\<USER>\Documents\Freela\apps\expert-dashboard\node_modules\react-dom\cjs\react-dom-server.browser.production.min.js:76:89)

   Generating static pages (2/5) 
    at Zc (C:\Users\<USER>\Documents\Freela\apps\expert-dashboard\node_modules\react-dom\cjs\react-dom-server.browser.production.min.js:74:209)

Error occurred prerendering page "/500". Read more: https://nextjs.org/docs/messages/prerender-error
TypeError: Cannot read properties of null (reading 'useContext')
    at exports.useContext (C:\Users\<USER>\Documents\Freela\node_modules\react\cjs\react.production.min.js:24:118)
    at StyleRegistry (C:\Users\<USER>\Documents\Freela\node_modules\styled-jsx\dist\index\index.js:450:30)
    at Wc (C:\Users\<USER>\Documents\Freela\apps\expert-dashboard\node_modules\react-dom\cjs\react-dom-server.browser.production.min.js:68:44)
    at Zc (C:\Users\<USER>\Documents\Freela\apps\expert-dashboard\node_modules\react-dom\cjs\react-dom-server.browser.production.min.js:70:253)
    at Z (C:\Users\<USER>\Documents\Freela\apps\expert-dashboard\node_modules\react-dom\cjs\react-dom-server.browser.production.min.js:76:89)
    at Zc (C:\Users\<USER>\Documents\Freela\apps\expert-dashboard\node_modules\react-dom\cjs\react-dom-server.browser.production.min.js:74:209)
    at Z (C:\Users\<USER>\Documents\Freela\apps\expert-dashboard\node_modules\react-dom\cjs\react-dom-server.browser.production.min.js:76:89)
    at Zc (C:\Users\<USER>\Documents\Freela\apps\expert-dashboard\node_modules\react-dom\cjs\react-dom-server.browser.production.min.js:74:209)
    at Z (C:\Users\<USER>\Documents\Freela\apps\expert-dashboard\node_modules\react-dom\cjs\react-dom-server.browser.production.min.js:76:89)
    at Zc (C:\Users\<USER>\Documents\Freela\apps\expert-dashboard\node_modules\react-dom\cjs\react-dom-server.browser.production.min.js:74:209)
 ⚠ Unsupported metadata viewport is configured in metadata export. Please move it to viewport export instead.
 ⚠ Unsupported metadata viewport is configured in metadata export. Please move it to viewport export instead.
 ⚠ Unsupported metadata viewport is configured in metadata export. Please move it to viewport export instead.
 ⚠ Unsupported metadata viewport is configured in metadata export. Please move it to viewport export instead.
 ⚠ Unsupported metadata viewport is configured in metadata export. Please move it to viewport export instead.
 ⚠ Unsupported metadata viewport is configured in metadata export. Please move it to viewport export instead.
 ⚠ Unsupported metadata viewport is configured in metadata export. Please move it to viewport export instead.

   Generating static pages (3/5) 

 ✓ Generating static pages (5/5) 

> Export encountered errors on following paths:
	/_error: /404
	/_error: /500
npm error Lifecycle script `build` failed with error:
npm error code 1
npm error path C:\Users\<USER>\Documents\Freela\apps\expert-dashboard
npm error workspace @freela/expert-dashboard@1.0.0
npm error location C:\Users\<USER>\Documents\Freela\apps\expert-dashboard
npm error command failed
npm error command C:\WINDOWS\system32\cmd.exe /d /s /c next build
