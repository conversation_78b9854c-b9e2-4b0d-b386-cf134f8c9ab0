export declare const formatDate: (date: Date | string, options?: Intl.DateTimeFormatOptions, language?: string) => string;
export declare const formatTime: (date: Date | string, options?: Intl.DateTimeFormatOptions, language?: string) => string;
export declare const formatDateTime: (date: Date | string, options?: Intl.DateTimeFormatOptions, language?: string) => string;
export declare const formatRelativeTime: (date: Date | string, language?: string) => string;
export declare const formatNumber: (number: number, options?: Intl.NumberFormatOptions, language?: string) => string;
export declare const formatCurrency: (amount: number, currency?: string, language?: string) => string;
export declare const formatPercentage: (value: number, options?: Intl.NumberFormatOptions, language?: string) => string;
export declare const truncateText: (text: string, maxLength: number, suffix?: string) => string;
export declare const capitalizeFirst: (text: string) => string;
export declare const getFlexDirection: (language?: string) => "row" | "row-reverse";
export declare const getMarginStart: (value: string | number, language?: string) => Record<string, any>;
export declare const getMarginEnd: (value: string | number, language?: string) => Record<string, any>;
export declare const getPaddingStart: (value: string | number, language?: string) => Record<string, any>;
export declare const getPaddingEnd: (value: string | number, language?: string) => Record<string, any>;
export declare const getBorderStart: (value: string, language?: string) => Record<string, any>;
export declare const getBorderEnd: (value: string, language?: string) => Record<string, any>;
export declare const transformStyleForRTL: (style: Record<string, any>, language?: string) => Record<string, any>;
export declare const validateArabicText: (text: string) => boolean;
export declare const validateEnglishText: (text: string) => boolean;
export declare const formatSyrianPhoneNumber: (phone: string) => string;
export declare const generateSlug: (text: string, language?: string) => string;
//# sourceMappingURL=utils.d.ts.map