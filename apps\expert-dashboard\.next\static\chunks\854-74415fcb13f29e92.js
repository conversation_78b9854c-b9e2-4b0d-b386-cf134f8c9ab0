(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[854],{6435:function(e,t,s){"use strict";s.d(t,{F:function(){return l},f:function(){return c}});var i=s(2265);let r=["light","dark"],n="(prefers-color-scheme: dark)",a="undefined"==typeof window,o=(0,i.createContext)(void 0),u={setTheme:e=>{},themes:[]},l=()=>{var e;return null!==(e=(0,i.useContext)(o))&&void 0!==e?e:u},c=e=>(0,i.useContext)(o)?i.createElement(i.Fragment,null,e.children):i.createElement(d,e),h=["light","dark"],d=({forcedTheme:e,disableTransitionOnChange:t=!1,enableSystem:s=!0,enableColorScheme:a=!0,storageKey:u="theme",themes:l=h,defaultTheme:c=s?"system":"light",attribute:d="data-theme",value:v,children:g,nonce:b})=>{let[w,C]=(0,i.useState)(()=>p(u,c)),[O,x]=(0,i.useState)(()=>p(u)),S=v?Object.values(v):l,E=(0,i.useCallback)(e=>{let i=e;if(!i)return;"system"===e&&s&&(i=y());let n=v?v[i]:i,o=t?m():null,u=document.documentElement;if("class"===d?(u.classList.remove(...S),n&&u.classList.add(n)):n?u.setAttribute(d,n):u.removeAttribute(d),a){let e=r.includes(c)?c:null,t=r.includes(i)?i:e;u.style.colorScheme=t}null==o||o()},[]),q=(0,i.useCallback)(e=>{C(e);try{localStorage.setItem(u,e)}catch(e){}},[e]),P=(0,i.useCallback)(t=>{let i=y(t);x(i),"system"===w&&s&&!e&&E("system")},[w,e]);(0,i.useEffect)(()=>{let e=window.matchMedia(n);return e.addListener(P),P(e),()=>e.removeListener(P)},[P]),(0,i.useEffect)(()=>{let e=e=>{e.key===u&&q(e.newValue||c)};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[q]),(0,i.useEffect)(()=>{E(null!=e?e:w)},[e,w]);let F=(0,i.useMemo)(()=>({theme:w,setTheme:q,forcedTheme:e,resolvedTheme:"system"===w?O:w,themes:s?[...l,"system"]:l,systemTheme:s?O:void 0}),[w,q,e,O,s,l]);return i.createElement(o.Provider,{value:F},i.createElement(f,{forcedTheme:e,disableTransitionOnChange:t,enableSystem:s,enableColorScheme:a,storageKey:u,themes:l,defaultTheme:c,attribute:d,value:v,children:g,attrs:S,nonce:b}),g)},f=(0,i.memo)(({forcedTheme:e,storageKey:t,attribute:s,enableSystem:a,enableColorScheme:o,defaultTheme:u,value:l,attrs:c,nonce:h})=>{let d="system"===u,f="class"===s?`var d=document.documentElement,c=d.classList;c.remove(${c.map(e=>`'${e}'`).join(",")});`:`var d=document.documentElement,n='${s}',s='setAttribute';`,p=o?r.includes(u)&&u?`if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'${u}'`:"if(e==='light'||e==='dark')d.style.colorScheme=e":"",m=(e,t=!1,i=!0)=>{let n=l?l[e]:e,a=t?e+"|| ''":`'${n}'`,u="";return o&&i&&!t&&r.includes(e)&&(u+=`d.style.colorScheme = '${e}';`),"class"===s?u+=t||n?`c.add(${a})`:"null":n&&(u+=`d[s](n,${a})`),u},y=e?`!function(){${f}${m(e)}}()`:a?`!function(){try{${f}var e=localStorage.getItem('${t}');if('system'===e||(!e&&${d})){var t='${n}',m=window.matchMedia(t);if(m.media!==t||m.matches){${m("dark")}}else{${m("light")}}}else if(e){${l?`var x=${JSON.stringify(l)};`:""}${m(l?"x[e]":"e",!0)}}${d?"":"else{"+m(u,!1,!1)+"}"}${p}}catch(e){}}()`:`!function(){try{${f}var e=localStorage.getItem('${t}');if(e){${l?`var x=${JSON.stringify(l)};`:""}${m(l?"x[e]":"e",!0)}}else{${m(u,!1,!1)};}${p}}catch(t){}}();`;return i.createElement("script",{nonce:h,dangerouslySetInnerHTML:{__html:y}})},()=>!0),p=(e,t)=>{let s;if(!a){try{s=localStorage.getItem(e)||void 0}catch(e){}return s||t}},m=()=>{let e=document.createElement("style");return e.appendChild(document.createTextNode("*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(e),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(e)},1)}},y=e=>(e||(e=window.matchMedia(n)),e.matches?"dark":"light")},5537:function(e){e.exports={style:{fontFamily:"'__Inter_e8ce0c', '__Inter_Fallback_e8ce0c'",fontStyle:"normal"},className:"__className_e8ce0c",variable:"__variable_e8ce0c"}},622:function(e,t,s){"use strict";var i=s(2265),r=Symbol.for("react.element"),n=Symbol.for("react.fragment"),a=Object.prototype.hasOwnProperty,o=i.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,u={key:!0,ref:!0,__self:!0,__source:!0};function l(e,t,s){var i,n={},l=null,c=null;for(i in void 0!==s&&(l=""+s),void 0!==t.key&&(l=""+t.key),void 0!==t.ref&&(c=t.ref),t)a.call(t,i)&&!u.hasOwnProperty(i)&&(n[i]=t[i]);if(e&&e.defaultProps)for(i in t=e.defaultProps)void 0===n[i]&&(n[i]=t[i]);return{$$typeof:r,type:e,key:l,ref:c,props:n,_owner:o.current}}t.Fragment=n,t.jsx=l,t.jsxs=l},7437:function(e,t,s){"use strict";e.exports=s(622)},3588:function(e,t,s){"use strict";let i,r;s.d(t,{x7:function(){return eh}});var n,a=s(2265);let o={data:""},u=e=>"object"==typeof window?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||o,l=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,c=/\/\*[^]*?\*\/|  +/g,h=/\n+/g,d=(e,t)=>{let s="",i="",r="";for(let n in e){let a=e[n];"@"==n[0]?"i"==n[1]?s=n+" "+a+";":i+="f"==n[1]?d(a,n):n+"{"+d(a,"k"==n[1]?"":t)+"}":"object"==typeof a?i+=d(a,t?t.replace(/([^,])+/g,e=>n.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,t=>/&/.test(t)?t.replace(/&/g,e):e?e+" "+t:t)):n):null!=a&&(n=/^--/.test(n)?n:n.replace(/[A-Z]/g,"-$&").toLowerCase(),r+=d.p?d.p(n,a):n+":"+a+";")}return s+(t&&r?t+"{"+r+"}":r)+i},f={},p=e=>{if("object"==typeof e){let t="";for(let s in e)t+=s+p(e[s]);return t}return e},m=(e,t,s,i,r)=>{var n;let a=p(e),o=f[a]||(f[a]=(e=>{let t=0,s=11;for(;t<e.length;)s=101*s+e.charCodeAt(t++)>>>0;return"go"+s})(a));if(!f[o]){let t=a!==e?e:(e=>{let t,s,i=[{}];for(;t=l.exec(e.replace(c,""));)t[4]?i.shift():t[3]?(s=t[3].replace(h," ").trim(),i.unshift(i[0][s]=i[0][s]||{})):i[0][t[1]]=t[2].replace(h," ").trim();return i[0]})(e);f[o]=d(r?{["@keyframes "+o]:t}:t,s?"":"."+o)}let u=s&&f.g?f.g:null;return s&&(f.g=f[o]),n=f[o],u?t.data=t.data.replace(u,n):-1===t.data.indexOf(n)&&(t.data=i?n+t.data:t.data+n),o},y=(e,t,s)=>e.reduce((e,i,r)=>{let n=t[r];if(n&&n.call){let e=n(s),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;n=t?"."+t:e&&"object"==typeof e?e.props?"":d(e,""):!1===e?"":e}return e+i+(null==n?"":n)},"");function v(e){let t=this||{},s=e.call?e(t.p):e;return m(s.unshift?s.raw?y(s,[].slice.call(arguments,1),t.p):s.reduce((e,s)=>Object.assign(e,s&&s.call?s(t.p):s),{}):s,u(t.target),t.g,t.o,t.k)}v.bind({g:1});let g,b,w,C=v.bind({k:1});function O(e,t){let s=this||{};return function(){let i=arguments;function r(n,a){let o=Object.assign({},n),u=o.className||r.className;s.p=Object.assign({theme:b&&b()},o),s.o=/ *go\d+/.test(u),o.className=v.apply(s,i)+(u?" "+u:""),t&&(o.ref=a);let l=e;return e[0]&&(l=o.as||e,delete o.as),w&&l[0]&&w(o),g(l,o)}return t?t(r):r}}var x=e=>"function"==typeof e,S=(e,t)=>x(e)?e(t):e,E=(i=0,()=>(++i).toString()),q=()=>{if(void 0===r&&"u">typeof window){let e=matchMedia("(prefers-reduced-motion: reduce)");r=!e||e.matches}return r},P=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,20)};case 1:return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case 2:let{toast:s}=t;return P(e,{type:e.toasts.find(e=>e.id===s.id)?1:0,toast:s});case 3:let{toastId:i}=t;return{...e,toasts:e.toasts.map(e=>e.id===i||void 0===i?{...e,dismissed:!0,visible:!1}:e)};case 4:return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let r=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(e=>({...e,pauseDuration:e.pauseDuration+r}))}}},F=[],D={toasts:[],pausedAt:void 0},T=e=>{D=P(D,e),F.forEach(e=>{e(D)})},k={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},A=(e={})=>{let[t,s]=(0,a.useState)(D),i=(0,a.useRef)(D);(0,a.useEffect)(()=>(i.current!==D&&s(D),F.push(s),()=>{let e=F.indexOf(s);e>-1&&F.splice(e,1)}),[]);let r=t.toasts.map(t=>{var s,i,r;return{...e,...e[t.type],...t,removeDelay:t.removeDelay||(null==(s=e[t.type])?void 0:s.removeDelay)||(null==e?void 0:e.removeDelay),duration:t.duration||(null==(i=e[t.type])?void 0:i.duration)||(null==e?void 0:e.duration)||k[t.type],style:{...e.style,...null==(r=e[t.type])?void 0:r.style,...t.style}}});return{...t,toasts:r}},$=(e,t="blank",s)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...s,id:(null==s?void 0:s.id)||E()}),M=e=>(t,s)=>{let i=$(t,e,s);return T({type:2,toast:i}),i.id},j=(e,t)=>M("blank")(e,t);j.error=M("error"),j.success=M("success"),j.loading=M("loading"),j.custom=M("custom"),j.dismiss=e=>{T({type:3,toastId:e})},j.remove=e=>T({type:4,toastId:e}),j.promise=(e,t,s)=>{let i=j.loading(t.loading,{...s,...null==s?void 0:s.loading});return"function"==typeof e&&(e=e()),e.then(e=>{let r=t.success?S(t.success,e):void 0;return r?j.success(r,{id:i,...s,...null==s?void 0:s.success}):j.dismiss(i),e}).catch(e=>{let r=t.error?S(t.error,e):void 0;r?j.error(r,{id:i,...s,...null==s?void 0:s.error}):j.dismiss(i)}),e};var _=(e,t)=>{T({type:1,toast:{id:e,height:t}})},R=()=>{T({type:5,time:Date.now()})},Q=new Map,I=1e3,L=(e,t=I)=>{if(Q.has(e))return;let s=setTimeout(()=>{Q.delete(e),T({type:4,toastId:e})},t);Q.set(e,s)},N=e=>{let{toasts:t,pausedAt:s}=A(e);(0,a.useEffect)(()=>{if(s)return;let e=Date.now(),i=t.map(t=>{if(t.duration===1/0)return;let s=(t.duration||0)+t.pauseDuration-(e-t.createdAt);if(s<0){t.visible&&j.dismiss(t.id);return}return setTimeout(()=>j.dismiss(t.id),s)});return()=>{i.forEach(e=>e&&clearTimeout(e))}},[t,s]);let i=(0,a.useCallback)(()=>{s&&T({type:6,time:Date.now()})},[s]),r=(0,a.useCallback)((e,s)=>{let{reverseOrder:i=!1,gutter:r=8,defaultPosition:n}=s||{},a=t.filter(t=>(t.position||n)===(e.position||n)&&t.height),o=a.findIndex(t=>t.id===e.id),u=a.filter((e,t)=>t<o&&e.visible).length;return a.filter(e=>e.visible).slice(...i?[u+1]:[0,u]).reduce((e,t)=>e+(t.height||0)+r,0)},[t]);return(0,a.useEffect)(()=>{t.forEach(e=>{if(e.dismissed)L(e.id,e.removeDelay);else{let t=Q.get(e.id);t&&(clearTimeout(t),Q.delete(e.id))}})},[t]),{toasts:t,handlers:{updateHeight:_,startPause:R,endPause:i,calculateOffset:r}}},U=C`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,K=C`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,H=C`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,G=O("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${U} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${K} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${H} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,z=C`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,B=O("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${z} 1s linear infinite;
`,J=C`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,W=C`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,Y=O("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${J} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${W} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,V=O("div")`
  position: absolute;
`,Z=O("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,X=C`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,ee=O("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${X} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,et=({toast:e})=>{let{icon:t,type:s,iconTheme:i}=e;return void 0!==t?"string"==typeof t?a.createElement(ee,null,t):t:"blank"===s?null:a.createElement(Z,null,a.createElement(B,{...i}),"loading"!==s&&a.createElement(V,null,"error"===s?a.createElement(G,{...i}):a.createElement(Y,{...i})))},es=e=>`
0% {transform: translate3d(0,${-200*e}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,ei=e=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${-150*e}%,-1px) scale(.6); opacity:0;}
`,er=O("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,en=O("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,ea=(e,t)=>{let s=e.includes("top")?1:-1,[i,r]=q()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[es(s),ei(s)];return{animation:t?`${C(i)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${C(r)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},eo=a.memo(({toast:e,position:t,style:s,children:i})=>{let r=e.height?ea(e.position||t||"top-center",e.visible):{opacity:0},n=a.createElement(et,{toast:e}),o=a.createElement(en,{...e.ariaProps},S(e.message,e));return a.createElement(er,{className:e.className,style:{...r,...s,...e.style}},"function"==typeof i?i({icon:n,message:o}):a.createElement(a.Fragment,null,n,o))});n=a.createElement,d.p=void 0,g=n,b=void 0,w=void 0;var eu=({id:e,className:t,style:s,onHeightUpdate:i,children:r})=>{let n=a.useCallback(t=>{if(t){let s=()=>{i(e,t.getBoundingClientRect().height)};s(),new MutationObserver(s).observe(t,{subtree:!0,childList:!0,characterData:!0})}},[e,i]);return a.createElement("div",{ref:n,className:t,style:s},r)},el=(e,t)=>{let s=e.includes("top"),i=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:q()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(s?1:-1)}px)`,...s?{top:0}:{bottom:0},...i}},ec=v`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,eh=({reverseOrder:e,position:t="top-center",toastOptions:s,gutter:i,children:r,containerStyle:n,containerClassName:o})=>{let{toasts:u,handlers:l}=N(s);return a.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:16,left:16,right:16,bottom:16,pointerEvents:"none",...n},className:o,onMouseEnter:l.startPause,onMouseLeave:l.endPause},u.map(s=>{let n=s.position||t,o=el(n,l.calculateOffset(s,{reverseOrder:e,gutter:i,defaultPosition:t}));return a.createElement(eu,{id:s.id,key:s.id,onHeightUpdate:l.updateHeight,className:s.visible?ec:"",style:o},"custom"===s.type?S(s.message,s):r?r(s):a.createElement(eo,{toast:s,position:n}))}))}},3115:function(e,t,s){"use strict";s.d(t,{S:function(){return R}});var i="undefined"==typeof window||"Deno"in globalThis;function r(){}function n(e,t){return"function"==typeof e?e(t):e}function a(e,t){let{type:s="all",exact:i,fetchStatus:r,predicate:n,queryKey:a,stale:o}=e;if(a){if(i){if(t.queryHash!==u(a,t.options))return!1}else if(!c(t.queryKey,a))return!1}if("all"!==s){let e=t.isActive();if("active"===s&&!e||"inactive"===s&&e)return!1}return("boolean"!=typeof o||t.isStale()===o)&&(!r||r===t.state.fetchStatus)&&(!n||!!n(t))}function o(e,t){let{exact:s,status:i,predicate:r,mutationKey:n}=e;if(n){if(!t.options.mutationKey)return!1;if(s){if(l(t.options.mutationKey)!==l(n))return!1}else if(!c(t.options.mutationKey,n))return!1}return(!i||t.state.status===i)&&(!r||!!r(t))}function u(e,t){let s=t?.queryKeyHashFn||l;return s(e)}function l(e){return JSON.stringify(e,(e,t)=>d(t)?Object.keys(t).sort().reduce((e,s)=>(e[s]=t[s],e),{}):t)}function c(e,t){return e===t||typeof e==typeof t&&!!e&&!!t&&"object"==typeof e&&"object"==typeof t&&Object.keys(t).every(s=>c(e[s],t[s]))}function h(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function d(e){if(!f(e))return!1;let t=e.constructor;if(void 0===t)return!0;let s=t.prototype;return!!(f(s)&&s.hasOwnProperty("isPrototypeOf"))&&Object.getPrototypeOf(e)===Object.prototype}function f(e){return"[object Object]"===Object.prototype.toString.call(e)}function p(e,t,s=0){let i=[...e,t];return s&&i.length>s?i.slice(1):i}function m(e,t,s=0){let i=[t,...e];return s&&i.length>s?i.slice(0,-1):i}var y=Symbol();function v(e,t){return!e.queryFn&&t?.initialPromise?()=>t.initialPromise:e.queryFn&&e.queryFn!==y?e.queryFn:()=>Promise.reject(Error(`Missing queryFn: '${e.queryHash}'`))}var g=e=>setTimeout(e,0),b=function(){let e=[],t=0,s=e=>{e()},i=e=>{e()},r=g,n=i=>{t?e.push(i):r(()=>{s(i)})},a=()=>{let t=e;e=[],t.length&&r(()=>{i(()=>{t.forEach(e=>{s(e)})})})};return{batch:e=>{let s;t++;try{s=e()}finally{--t||a()}return s},batchCalls:e=>(...t)=>{n(()=>{e(...t)})},schedule:n,setNotifyFunction:e=>{s=e},setBatchNotifyFunction:e=>{i=e},setScheduler:e=>{r=e}}}(),w=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},C=new class extends w{#e;#t;#s;constructor(){super(),this.#s=e=>{if(!i&&window.addEventListener){let t=()=>e();return window.addEventListener("visibilitychange",t,!1),()=>{window.removeEventListener("visibilitychange",t)}}}}onSubscribe(){this.#t||this.setEventListener(this.#s)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#s=e,this.#t?.(),this.#t=e(e=>{"boolean"==typeof e?this.setFocused(e):this.onFocus()})}setFocused(e){let t=this.#e!==e;t&&(this.#e=e,this.onFocus())}onFocus(){let e=this.isFocused();this.listeners.forEach(t=>{t(e)})}isFocused(){return"boolean"==typeof this.#e?this.#e:globalThis.document?.visibilityState!=="hidden"}},O=new class extends w{#i=!0;#t;#s;constructor(){super(),this.#s=e=>{if(!i&&window.addEventListener){let t=()=>e(!0),s=()=>e(!1);return window.addEventListener("online",t,!1),window.addEventListener("offline",s,!1),()=>{window.removeEventListener("online",t),window.removeEventListener("offline",s)}}}}onSubscribe(){this.#t||this.setEventListener(this.#s)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#s=e,this.#t?.(),this.#t=e(this.setOnline.bind(this))}setOnline(e){let t=this.#i!==e;t&&(this.#i=e,this.listeners.forEach(t=>{t(e)}))}isOnline(){return this.#i}};function x(e){return Math.min(1e3*2**e,3e4)}function S(e){return(e??"online")!=="online"||O.isOnline()}var E=class extends Error{constructor(e){super("CancelledError"),this.revert=e?.revert,this.silent=e?.silent}};function q(e){return e instanceof E}function P(e){let t,s=!1,r=0,n=!1,a=function(){let e,t;let s=new Promise((s,i)=>{e=s,t=i});function i(e){Object.assign(s,e),delete s.resolve,delete s.reject}return s.status="pending",s.catch(()=>{}),s.resolve=t=>{i({status:"fulfilled",value:t}),e(t)},s.reject=e=>{i({status:"rejected",reason:e}),t(e)},s}(),o=()=>C.isFocused()&&("always"===e.networkMode||O.isOnline())&&e.canRun(),u=()=>S(e.networkMode)&&e.canRun(),l=s=>{n||(n=!0,e.onSuccess?.(s),t?.(),a.resolve(s))},c=s=>{n||(n=!0,e.onError?.(s),t?.(),a.reject(s))},h=()=>new Promise(s=>{t=e=>{(n||o())&&s(e)},e.onPause?.()}).then(()=>{t=void 0,n||e.onContinue?.()}),d=()=>{let t;if(n)return;let a=0===r?e.initialPromise:void 0;try{t=a??e.fn()}catch(e){t=Promise.reject(e)}Promise.resolve(t).then(l).catch(t=>{if(n)return;let a=e.retry??(i?0:3),u=e.retryDelay??x,l="function"==typeof u?u(r,t):u,f=!0===a||"number"==typeof a&&r<a||"function"==typeof a&&a(r,t);if(s||!f){c(t);return}r++,e.onFail?.(r,t),new Promise(e=>{setTimeout(e,l)}).then(()=>o()?void 0:h()).then(()=>{s?c(t):d()})})};return{promise:a,cancel:t=>{n||(c(new E(t)),e.abort?.())},continue:()=>(t?.(),a),cancelRetry:()=>{s=!0},continueRetry:()=>{s=!1},canStart:u,start:()=>(u()?d():h().then(d),a)}}var F=class{#r;destroy(){this.clearGcTimeout()}scheduleGc(){var e;this.clearGcTimeout(),"number"==typeof(e=this.gcTime)&&e>=0&&e!==1/0&&(this.#r=setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(i?1/0:3e5))}clearGcTimeout(){this.#r&&(clearTimeout(this.#r),this.#r=void 0)}},D=class extends F{#n;#a;#o;#u;#l;#c;#h;constructor(e){super(),this.#h=!1,this.#c=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.#u=e.client,this.#o=this.#u.getQueryCache(),this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.#n=function(e){let t="function"==typeof e.initialData?e.initialData():e.initialData,s=void 0!==t,i=s?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:s?i??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:s?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=e.state??this.#n,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#l?.promise}setOptions(e){this.options={...this.#c,...e},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#o.remove(this)}setData(e,t){var s,i;let r=(s=this.state.data,"function"==typeof(i=this.options).structuralSharing?i.structuralSharing(s,e):!1!==i.structuralSharing?function e(t,s){if(t===s)return t;let i=h(t)&&h(s);if(i||d(t)&&d(s)){let r=i?t:Object.keys(t),n=r.length,a=i?s:Object.keys(s),o=a.length,u=i?[]:{},l=new Set(r),c=0;for(let r=0;r<o;r++){let n=i?r:a[r];(!i&&l.has(n)||i)&&void 0===t[n]&&void 0===s[n]?(u[n]=void 0,c++):(u[n]=e(t[n],s[n]),u[n]===t[n]&&void 0!==t[n]&&c++)}return n===o&&c===n?t:u}return s}(s,e):e);return this.#d({data:r,type:"success",dataUpdatedAt:t?.updatedAt,manual:t?.manual}),r}setState(e,t){this.#d({type:"setState",state:e,setStateOptions:t})}cancel(e){let t=this.#l?.promise;return this.#l?.cancel(e),t?t.then(r).catch(r):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#n)}isActive(){return this.observers.some(e=>{var t;return!1!==("function"==typeof(t=e.options.enabled)?t(this):t)})}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===y||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0&&this.observers.some(e=>"static"===n(e.options.staleTime,this))}isStale(){return this.getObserversCount()>0?this.observers.some(e=>e.getCurrentResult().isStale):void 0===this.state.data||this.state.isInvalidated}isStaleByTime(e=0){return void 0===this.state.data||"static"!==e&&(!!this.state.isInvalidated||!Math.max(this.state.dataUpdatedAt+(e||0)-Date.now(),0))}onFocus(){let e=this.observers.find(e=>e.shouldFetchOnWindowFocus());e?.refetch({cancelRefetch:!1}),this.#l?.continue()}onOnline(){let e=this.observers.find(e=>e.shouldFetchOnReconnect());e?.refetch({cancelRefetch:!1}),this.#l?.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.#o.notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter(t=>t!==e),this.observers.length||(this.#l&&(this.#h?this.#l.cancel({revert:!0}):this.#l.cancelRetry()),this.scheduleGc()),this.#o.notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#d({type:"invalidate"})}fetch(e,t){if("idle"!==this.state.fetchStatus){if(void 0!==this.state.data&&t?.cancelRefetch)this.cancel({silent:!0});else if(this.#l)return this.#l.continueRetry(),this.#l.promise}if(e&&this.setOptions(e),!this.options.queryFn){let e=this.observers.find(e=>e.options.queryFn);e&&this.setOptions(e.options)}let s=new AbortController,i=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(this.#h=!0,s.signal)})},r=()=>{let e=v(this.options,t),s=(()=>{let e={client:this.#u,queryKey:this.queryKey,meta:this.meta};return i(e),e})();return(this.#h=!1,this.options.persister)?this.options.persister(e,s,this):e(s)},n=(()=>{let e={fetchOptions:t,options:this.options,queryKey:this.queryKey,client:this.#u,state:this.state,fetchFn:r};return i(e),e})();this.options.behavior?.onFetch(n,this),this.#a=this.state,("idle"===this.state.fetchStatus||this.state.fetchMeta!==n.fetchOptions?.meta)&&this.#d({type:"fetch",meta:n.fetchOptions?.meta});let a=e=>{q(e)&&e.silent||this.#d({type:"error",error:e}),q(e)||(this.#o.config.onError?.(e,this),this.#o.config.onSettled?.(this.state.data,e,this)),this.scheduleGc()};return this.#l=P({initialPromise:t?.initialPromise,fn:n.fetchFn,abort:s.abort.bind(s),onSuccess:e=>{if(void 0===e){a(Error(`${this.queryHash} data is undefined`));return}try{this.setData(e)}catch(e){a(e);return}this.#o.config.onSuccess?.(e,this),this.#o.config.onSettled?.(e,this.state.error,this),this.scheduleGc()},onError:a,onFail:(e,t)=>{this.#d({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#d({type:"pause"})},onContinue:()=>{this.#d({type:"continue"})},retry:n.options.retry,retryDelay:n.options.retryDelay,networkMode:n.options.networkMode,canRun:()=>!0}),this.#l.start()}#d(e){this.state=(t=>{switch(e.type){case"failed":return{...t,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...t,fetchStatus:"paused"};case"continue":return{...t,fetchStatus:"fetching"};case"fetch":var s;return{...t,...(s=t.data,{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:S(this.options.networkMode)?"fetching":"paused",...void 0===s&&{error:null,status:"pending"}}),fetchMeta:e.meta??null};case"success":return{...t,data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:e.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":let i=e.error;if(q(i)&&i.revert&&this.#a)return{...this.#a,fetchStatus:"idle"};return{...t,error:i,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,fetchFailureReason:i,fetchStatus:"idle",status:"error"};case"invalidate":return{...t,isInvalidated:!0};case"setState":return{...t,...e.state}}})(this.state),b.batch(()=>{this.observers.forEach(e=>{e.onQueryUpdate()}),this.#o.notify({query:this,type:"updated",action:e})})}},T=class extends w{constructor(e={}){super(),this.config=e,this.#f=new Map}#f;build(e,t,s){let i=t.queryKey,r=t.queryHash??u(i,t),n=this.get(r);return n||(n=new D({client:e,queryKey:i,queryHash:r,options:e.defaultQueryOptions(t),state:s,defaultOptions:e.getQueryDefaults(i)}),this.add(n)),n}add(e){this.#f.has(e.queryHash)||(this.#f.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){let t=this.#f.get(e.queryHash);t&&(e.destroy(),t===e&&this.#f.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){b.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}get(e){return this.#f.get(e)}getAll(){return[...this.#f.values()]}find(e){let t={exact:!0,...e};return this.getAll().find(e=>a(t,e))}findAll(e={}){let t=this.getAll();return Object.keys(e).length>0?t.filter(t=>a(e,t)):t}notify(e){b.batch(()=>{this.listeners.forEach(t=>{t(e)})})}onFocus(){b.batch(()=>{this.getAll().forEach(e=>{e.onFocus()})})}onOnline(){b.batch(()=>{this.getAll().forEach(e=>{e.onOnline()})})}},k=class extends F{#p;#m;#l;constructor(e){super(),this.mutationId=e.mutationId,this.#m=e.mutationCache,this.#p=[],this.state=e.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#p.includes(e)||(this.#p.push(e),this.clearGcTimeout(),this.#m.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#p=this.#p.filter(t=>t!==e),this.scheduleGc(),this.#m.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#p.length||("pending"===this.state.status?this.scheduleGc():this.#m.remove(this))}continue(){return this.#l?.continue()??this.execute(this.state.variables)}async execute(e){let t=()=>{this.#d({type:"continue"})};this.#l=P({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(Error("No mutationFn found")),onFail:(e,t)=>{this.#d({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#d({type:"pause"})},onContinue:t,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#m.canRun(this)});let s="pending"===this.state.status,i=!this.#l.canStart();try{if(s)t();else{this.#d({type:"pending",variables:e,isPaused:i}),await this.#m.config.onMutate?.(e,this);let t=await this.options.onMutate?.(e);t!==this.state.context&&this.#d({type:"pending",context:t,variables:e,isPaused:i})}let r=await this.#l.start();return await this.#m.config.onSuccess?.(r,e,this.state.context,this),await this.options.onSuccess?.(r,e,this.state.context),await this.#m.config.onSettled?.(r,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(r,null,e,this.state.context),this.#d({type:"success",data:r}),r}catch(t){try{throw await this.#m.config.onError?.(t,e,this.state.context,this),await this.options.onError?.(t,e,this.state.context),await this.#m.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,t,e,this.state.context),t}finally{this.#d({type:"error",error:t})}}finally{this.#m.runNext(this)}}#d(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),b.batch(()=>{this.#p.forEach(t=>{t.onMutationUpdate(e)}),this.#m.notify({mutation:this,type:"updated",action:e})})}},A=class extends w{constructor(e={}){super(),this.config=e,this.#y=new Set,this.#v=new Map,this.#g=0}#y;#v;#g;build(e,t,s){let i=new k({mutationCache:this,mutationId:++this.#g,options:e.defaultMutationOptions(t),state:s});return this.add(i),i}add(e){this.#y.add(e);let t=$(e);if("string"==typeof t){let s=this.#v.get(t);s?s.push(e):this.#v.set(t,[e])}this.notify({type:"added",mutation:e})}remove(e){if(this.#y.delete(e)){let t=$(e);if("string"==typeof t){let s=this.#v.get(t);if(s){if(s.length>1){let t=s.indexOf(e);-1!==t&&s.splice(t,1)}else s[0]===e&&this.#v.delete(t)}}}this.notify({type:"removed",mutation:e})}canRun(e){let t=$(e);if("string"!=typeof t)return!0;{let s=this.#v.get(t),i=s?.find(e=>"pending"===e.state.status);return!i||i===e}}runNext(e){let t=$(e);if("string"!=typeof t)return Promise.resolve();{let s=this.#v.get(t)?.find(t=>t!==e&&t.state.isPaused);return s?.continue()??Promise.resolve()}}clear(){b.batch(()=>{this.#y.forEach(e=>{this.notify({type:"removed",mutation:e})}),this.#y.clear(),this.#v.clear()})}getAll(){return Array.from(this.#y)}find(e){let t={exact:!0,...e};return this.getAll().find(e=>o(t,e))}findAll(e={}){return this.getAll().filter(t=>o(e,t))}notify(e){b.batch(()=>{this.listeners.forEach(t=>{t(e)})})}resumePausedMutations(){let e=this.getAll().filter(e=>e.state.isPaused);return b.batch(()=>Promise.all(e.map(e=>e.continue().catch(r))))}};function $(e){return e.options.scope?.id}function M(e){return{onFetch:(t,s)=>{let i=t.options,r=t.fetchOptions?.meta?.fetchMore?.direction,n=t.state.data?.pages||[],a=t.state.data?.pageParams||[],o={pages:[],pageParams:[]},u=0,l=async()=>{let s=!1,l=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(t.signal.aborted?s=!0:t.signal.addEventListener("abort",()=>{s=!0}),t.signal)})},c=v(t.options,t.fetchOptions),h=async(e,i,r)=>{if(s)return Promise.reject();if(null==i&&e.pages.length)return Promise.resolve(e);let n=(()=>{let e={client:t.client,queryKey:t.queryKey,pageParam:i,direction:r?"backward":"forward",meta:t.options.meta};return l(e),e})(),a=await c(n),{maxPages:o}=t.options,u=r?m:p;return{pages:u(e.pages,a,o),pageParams:u(e.pageParams,i,o)}};if(r&&n.length){let e="backward"===r,t=e?_:j,s={pages:n,pageParams:a},u=t(i,s);o=await h(s,u,e)}else{let t=e??n.length;do{let e=0===u?a[0]??i.initialPageParam:j(i,o);if(u>0&&null==e)break;o=await h(o,e),u++}while(u<t)}return o};t.options.persister?t.fetchFn=()=>t.options.persister?.(l,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},s):t.fetchFn=l}}}function j(e,{pages:t,pageParams:s}){let i=t.length-1;return t.length>0?e.getNextPageParam(t[i],t,s[i],s):void 0}function _(e,{pages:t,pageParams:s}){return t.length>0?e.getPreviousPageParam?.(t[0],t,s[0],s):void 0}var R=class{#b;#m;#c;#w;#C;#O;#x;#S;constructor(e={}){this.#b=e.queryCache||new T,this.#m=e.mutationCache||new A,this.#c=e.defaultOptions||{},this.#w=new Map,this.#C=new Map,this.#O=0}mount(){this.#O++,1===this.#O&&(this.#x=C.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#b.onFocus())}),this.#S=O.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#b.onOnline())}))}unmount(){this.#O--,0===this.#O&&(this.#x?.(),this.#x=void 0,this.#S?.(),this.#S=void 0)}isFetching(e){return this.#b.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#m.findAll({...e,status:"pending"}).length}getQueryData(e){let t=this.defaultQueryOptions({queryKey:e});return this.#b.get(t.queryHash)?.state.data}ensureQueryData(e){let t=this.defaultQueryOptions(e),s=this.#b.build(this,t),i=s.state.data;return void 0===i?this.fetchQuery(e):(e.revalidateIfStale&&s.isStaleByTime(n(t.staleTime,s))&&this.prefetchQuery(t),Promise.resolve(i))}getQueriesData(e){return this.#b.findAll(e).map(({queryKey:e,state:t})=>{let s=t.data;return[e,s]})}setQueryData(e,t,s){let i=this.defaultQueryOptions({queryKey:e}),r=this.#b.get(i.queryHash),n=r?.state.data,a="function"==typeof t?t(n):t;if(void 0!==a)return this.#b.build(this,i).setData(a,{...s,manual:!0})}setQueriesData(e,t,s){return b.batch(()=>this.#b.findAll(e).map(({queryKey:e})=>[e,this.setQueryData(e,t,s)]))}getQueryState(e){let t=this.defaultQueryOptions({queryKey:e});return this.#b.get(t.queryHash)?.state}removeQueries(e){let t=this.#b;b.batch(()=>{t.findAll(e).forEach(e=>{t.remove(e)})})}resetQueries(e,t){let s=this.#b;return b.batch(()=>(s.findAll(e).forEach(e=>{e.reset()}),this.refetchQueries({type:"active",...e},t)))}cancelQueries(e,t={}){let s={revert:!0,...t},i=b.batch(()=>this.#b.findAll(e).map(e=>e.cancel(s)));return Promise.all(i).then(r).catch(r)}invalidateQueries(e,t={}){return b.batch(()=>(this.#b.findAll(e).forEach(e=>{e.invalidate()}),e?.refetchType==="none")?Promise.resolve():this.refetchQueries({...e,type:e?.refetchType??e?.type??"active"},t))}refetchQueries(e,t={}){let s={...t,cancelRefetch:t.cancelRefetch??!0},i=b.batch(()=>this.#b.findAll(e).filter(e=>!e.isDisabled()&&!e.isStatic()).map(e=>{let t=e.fetch(void 0,s);return s.throwOnError||(t=t.catch(r)),"paused"===e.state.fetchStatus?Promise.resolve():t}));return Promise.all(i).then(r)}fetchQuery(e){let t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);let s=this.#b.build(this,t);return s.isStaleByTime(n(t.staleTime,s))?s.fetch(t):Promise.resolve(s.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(r).catch(r)}fetchInfiniteQuery(e){return e.behavior=M(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(r).catch(r)}ensureInfiniteQueryData(e){return e.behavior=M(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return O.isOnline()?this.#m.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#b}getMutationCache(){return this.#m}getDefaultOptions(){return this.#c}setDefaultOptions(e){this.#c=e}setQueryDefaults(e,t){this.#w.set(l(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){let t=[...this.#w.values()],s={};return t.forEach(t=>{c(e,t.queryKey)&&Object.assign(s,t.defaultOptions)}),s}setMutationDefaults(e,t){this.#C.set(l(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){let t=[...this.#C.values()],s={};return t.forEach(t=>{c(e,t.mutationKey)&&Object.assign(s,t.defaultOptions)}),s}defaultQueryOptions(e){if(e._defaulted)return e;let t={...this.#c.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=u(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===y&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#c.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#b.clear(),this.#m.clear()}}},8650:function(e,t,s){"use strict";s.d(t,{aH:function(){return a}});var i=s(2265),r=s(7437),n=i.createContext(void 0),a=({client:e,children:t})=>(i.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),(0,r.jsx)(n.Provider,{value:e,children:t}))}}]);