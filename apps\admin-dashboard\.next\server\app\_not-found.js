(()=>{var e={};e.id=165,e.ids=[165],e.modules={5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},1877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},5319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},4706:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>p,pages:()=>c,routeModule:()=>m,tree:()=>l});var s=t(7096),a=t(6132),n=t(7284),i=t.n(n),o=t(2564),d={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);t.d(r,d);let l=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.t.bind(t,9291,23)),"next/dist/client/components/not-found-error"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,2594)),"C:\\Users\\<USER>\\Documents\\Freela\\apps\\admin-dashboard\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,9291,23)),"next/dist/client/components/not-found-error"]}],c=[],p="/_not-found",u={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/_not-found",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},3526:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,3579,23)),Promise.resolve().then(t.t.bind(t,619,23)),Promise.resolve().then(t.t.bind(t,1459,23)),Promise.resolve().then(t.t.bind(t,3456,23)),Promise.resolve().then(t.t.bind(t,847,23)),Promise.resolve().then(t.t.bind(t,7303,23))},2757:(e,r,t)=>{Promise.resolve().then(t.bind(t,5068))},5068:(e,r,t)=>{"use strict";t.r(r),t.d(r,{Providers:()=>l});var s=t(3854),a=t(5195),n=t(5045),i=t(253),o=t(7557),d=t(4218);function l({children:e}){let[r]=(0,d.useState)(()=>new a.S({defaultOptions:{queries:{staleTime:6e4,retry:1}}}));return s.jsx(n.aH,{client:r,children:(0,s.jsxs)(i.f,{attribute:"class",defaultTheme:"dark",enableSystem:!1,disableTransitionOnChange:!0,children:[e,s.jsx(o.x7,{position:"top-center",toastOptions:{duration:4e3,style:{background:"#1f2937",color:"#f9fafb",border:"1px solid #374151"},success:{iconTheme:{primary:"#10b981",secondary:"#f9fafb"}},error:{iconTheme:{primary:"#ef4444",secondary:"#f9fafb"}}}})]})})}},2594:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u,metadata:()=>p});var s=t(4656),a=t(4302),n=t.n(a),i=t(5153);let o=(0,i.createProxy)(String.raw`C:\Users\<USER>\Documents\Freela\apps\admin-dashboard\src\app\providers.tsx`),{__esModule:d,$$typeof:l}=o;o.default;let c=(0,i.createProxy)(String.raw`C:\Users\<USER>\Documents\Freela\apps\admin-dashboard\src\app\providers.tsx#Providers`);t(5023);let p={title:"Freela Syria - Admin Dashboard",description:"Administrative dashboard for Freela Syria marketplace",keywords:["freelance","syria","admin","dashboard"],authors:[{name:"Freela Syria Team"}],viewport:"width=device-width, initial-scale=1"};function u({children:e}){return(0,s.jsxs)("html",{lang:"ar",dir:"rtl",suppressHydrationWarning:!0,children:[s.jsx("head",{children:s.jsx("link",{href:"https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap",rel:"stylesheet"})}),s.jsx("body",{className:`${n().variable} font-arabic antialiased`,children:s.jsx(c,{children:e})})]})}},5023:()=>{}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[245],()=>t(4706));module.exports=s})();