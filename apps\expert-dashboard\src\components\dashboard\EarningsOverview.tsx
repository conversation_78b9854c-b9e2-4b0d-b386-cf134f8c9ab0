'use client';

import { CurrencyDollarIcon, ArrowTrendingUpIcon, ClockIcon } from '@heroicons/react/24/outline';

const earningsData = {
  thisMonth: 2450,
  lastMonth: 1890,
  pending: 340,
  available: 2110,
  growth: 29.6,
};

export function EarningsOverview() {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          نظرة عامة على الأرباح
        </h3>
        <CurrencyDollarIcon className="h-6 w-6 text-gray-400" />
      </div>
      
      <div className="space-y-4">
        {/* This month earnings */}
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-gray-500 dark:text-gray-400">أرباح هذا الشهر</p>
            <p className="text-2xl font-bold text-gray-900 dark:text-white">
              ${earningsData.thisMonth.toLocaleString()}
            </p>
          </div>
          <div className="flex items-center text-green-600 dark:text-green-400">
            <ArrowTrendingUpIcon className="h-4 w-4 ml-1 rtl:ml-0 rtl:mr-1" />
            <span className="text-sm font-medium">+{earningsData.growth}%</span>
          </div>
        </div>

        {/* Pending and available */}
        <div className="grid grid-cols-2 gap-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <div>
            <div className="flex items-center">
              <ClockIcon className="h-4 w-4 text-yellow-500 ml-1 rtl:ml-0 rtl:mr-1" />
              <p className="text-sm text-gray-500 dark:text-gray-400">في الانتظار</p>
            </div>
            <p className="text-lg font-semibold text-gray-900 dark:text-white">
              ${earningsData.pending}
            </p>
          </div>
          <div>
            <div className="flex items-center">
              <div className="h-4 w-4 bg-green-500 rounded-full ml-1 rtl:ml-0 rtl:mr-1"></div>
              <p className="text-sm text-gray-500 dark:text-gray-400">متاح للسحب</p>
            </div>
            <p className="text-lg font-semibold text-gray-900 dark:text-white">
              ${earningsData.available}
            </p>
          </div>
        </div>

        {/* Action button */}
        <button className="w-full mt-4 bg-primary-600 hover:bg-primary-700 text-white py-2 px-4 rounded-md text-sm font-medium transition-colors">
          سحب الأرباح
        </button>
      </div>
    </div>
  );
}
