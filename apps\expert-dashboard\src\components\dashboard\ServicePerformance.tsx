'use client';

import { EyeIcon, HeartIcon, ShoppingCartIcon } from '@heroicons/react/24/outline';

const services = [
  {
    id: 1,
    title: 'تصميم مواقع الويب',
    views: 234,
    likes: 45,
    bookings: 12,
    revenue: 1200,
    status: 'active',
  },
  {
    id: 2,
    title: 'تطوير تطبيقات الموبايل',
    views: 189,
    likes: 32,
    bookings: 8,
    revenue: 800,
    status: 'active',
  },
  {
    id: 3,
    title: 'استشارات تقنية',
    views: 156,
    likes: 28,
    bookings: 15,
    revenue: 450,
    status: 'active',
  },
];

export function ServicePerformance() {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          أداء الخدمات
        </h3>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          إحصائيات خدماتك الأكثر شيوعاً
        </p>
      </div>
      
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead className="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                الخدمة
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                المشاهدات
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                الإعجابات
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                الحجوزات
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                الإيرادات
              </th>
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            {services.map((service) => (
              <tr key={service.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900 dark:text-white">
                    {service.title}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      service.status === 'active' 
                        ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'
                        : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                    }`}>
                      {service.status === 'active' ? 'نشطة' : 'غير نشطة'}
                    </span>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center text-sm text-gray-900 dark:text-white">
                    <EyeIcon className="h-4 w-4 text-gray-400 ml-1 rtl:ml-0 rtl:mr-1" />
                    {service.views}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center text-sm text-gray-900 dark:text-white">
                    <HeartIcon className="h-4 w-4 text-red-400 ml-1 rtl:ml-0 rtl:mr-1" />
                    {service.likes}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center text-sm text-gray-900 dark:text-white">
                    <ShoppingCartIcon className="h-4 w-4 text-blue-400 ml-1 rtl:ml-0 rtl:mr-1" />
                    {service.bookings}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                  ${service.revenue}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
