import React from 'react';
export interface TransProps {
    i18nKey: string;
    values?: Record<string, any>;
    components?: Record<string, React.ReactElement>;
    ns?: string;
    className?: string;
    style?: React.CSSProperties;
}
export declare const Trans: React.FC<TransProps>;
export interface RTLTextProps {
    children: React.ReactNode;
    className?: string;
    style?: React.CSSProperties;
    as?: keyof JSX.IntrinsicElements;
}
export declare const RTLText: React.FC<RTLTextProps>;
export interface LanguageSwitcherProps {
    className?: string;
    style?: React.CSSProperties;
    showLabels?: boolean;
    variant?: 'dropdown' | 'buttons';
}
export declare const LanguageSwitcher: React.FC<LanguageSwitcherProps>;
export interface LocalizedDateProps {
    date: Date | string;
    format?: 'short' | 'medium' | 'long' | 'full';
    relative?: boolean;
    className?: string;
    style?: React.CSSProperties;
}
export declare const LocalizedDate: React.FC<LocalizedDateProps>;
export interface LocalizedNumberProps {
    value: number;
    type?: 'number' | 'currency' | 'percentage';
    currency?: string;
    minimumFractionDigits?: number;
    maximumFractionDigits?: number;
    className?: string;
    style?: React.CSSProperties;
}
export declare const LocalizedNumber: React.FC<LocalizedNumberProps>;
export interface DirectionProviderProps {
    children: React.ReactNode;
    force?: 'ltr' | 'rtl';
}
export declare const DirectionProvider: React.FC<DirectionProviderProps>;
//# sourceMappingURL=components.d.ts.map