'use client';

import { CalendarIcon, CheckCircleIcon, ClockIcon, XCircleIcon } from '@heroicons/react/24/outline';

const bookingStats = {
  total: 47,
  completed: 32,
  pending: 8,
  cancelled: 7,
  thisWeek: 12,
};

export function BookingStats() {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          إحصائيات الحجوزات
        </h3>
        <CalendarIcon className="h-6 w-6 text-gray-400" />
      </div>
      
      <div className="space-y-4">
        {/* Total bookings this month */}
        <div>
          <p className="text-sm text-gray-500 dark:text-gray-400">إجمالي الحجوزات هذا الشهر</p>
          <p className="text-2xl font-bold text-gray-900 dark:text-white">
            {bookingStats.total}
          </p>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            {bookingStats.thisWeek} حجوزات هذا الأسبوع
          </p>
        </div>

        {/* Booking breakdown */}
        <div className="grid grid-cols-3 gap-3 pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="text-center">
            <div className="flex items-center justify-center mb-1">
              <CheckCircleIcon className="h-4 w-4 text-green-500" />
            </div>
            <p className="text-lg font-semibold text-gray-900 dark:text-white">
              {bookingStats.completed}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400">مكتملة</p>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center mb-1">
              <ClockIcon className="h-4 w-4 text-yellow-500" />
            </div>
            <p className="text-lg font-semibold text-gray-900 dark:text-white">
              {bookingStats.pending}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400">في الانتظار</p>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center mb-1">
              <XCircleIcon className="h-4 w-4 text-red-500" />
            </div>
            <p className="text-lg font-semibold text-gray-900 dark:text-white">
              {bookingStats.cancelled}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400">ملغية</p>
          </div>
        </div>

        {/* Success rate */}
        <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-500 dark:text-gray-400">معدل النجاح</span>
            <span className="text-sm font-medium text-green-600 dark:text-green-400">
              {Math.round((bookingStats.completed / bookingStats.total) * 100)}%
            </span>
          </div>
          <div className="mt-2 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              className="bg-green-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${(bookingStats.completed / bookingStats.total) * 100}%` }}
            ></div>
          </div>
        </div>
      </div>
    </div>
  );
}
